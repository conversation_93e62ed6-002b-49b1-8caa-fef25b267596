// OpportunityService Extensions
export interface OpportunityDetails {
  id: string;
  name: string;
  stageName: string;
  amount: number;
  closeDate: Date;
  accountId: string;
  account: {
    name: string;
  };
  dateOfInjury: Date;
  accidentState: string;
  caseType: string;
  injurySeverity: number;
  liabilityPercentage: number;
  medicalLimit: string;
  claimNumber: string;
  accidentReportNumber: string;
}

export interface InsuranceInfo {
  insuranceCompany1: string;
  insuranceCompany2: string;
  insuranceLimits1: string;
  insuranceLimits2: string;
  policyType1: string;
  policyType2: string;
  decPage: boolean;
  decPageVerbal: boolean;
}

export interface CaseInfo {
  injuries: string;
  injuryDetails: string;
  incidentDescription: string;
  policeReportNumber: string;
  policeReportDetails: string;
}

export interface PlaintiffDetails {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  address: string;
  city: string;
  state: string;
  zip: string;
  email: string;
  phone: string;
}

export interface FundingRecord {
  id: string;
  type: string;
  amount: number;
  dateCreated: Date;
  status: string;
  balance: number;
}

export interface UnderwritingMetrics {
  riskScore: number;
  riskLevel: string;
  recommendedAmount: number;
  riskFactors: string[];
}

// Service Methods to Add
@Injectable({
  providedIn: 'root',
})
export class OpportunityService {
  
  getOpportunityDetails(opportunityId: string): Observable<OpportunityDetails> {
    return this.http.get<OpportunityDetails>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/details`
    );
  }

  getPlaintiffDetails(opportunityId: string): Observable<PlaintiffDetails> {
    return this.http.get<PlaintiffDetails>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/plaintiff`
    );
  }

  getInsuranceInfo(opportunityId: string): Observable<InsuranceInfo> {
    return this.http.get<InsuranceInfo>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/insurance`
    );
  }

  getCaseInformation(opportunityId: string): Observable<CaseInfo> {
    return this.http.get<CaseInfo>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/case-info`
    );
  }

  getFundingRecords(opportunityId: string): Observable<FundingRecord[]> {
    return this.http.get<FundingRecord[]>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/funding-records`
    );
  }

  getUnderwritingMetrics(opportunityId: string): Observable<UnderwritingMetrics> {
    return this.http.get<UnderwritingMetrics>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/underwriting-metrics`
    );
  }

  // Enhanced existing methods
  getAttachmentsByOpportunity(opportunityId: string): Observable<PatientAttachment[]> {
    return this.http.get<PatientAttachment[]>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/attachments`
    );
  }

  getProvidersByOpportunity(opportunityId: string): Observable<ProviderAccount[]> {
    return this.http.get<ProviderAccount[]>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/providers`
    );
  }
}

// Enhanced AttorneyService for better law firm integration
@Injectable({
  providedIn: 'root',
})
export class AttorneyService {
  
  getLawFirmByOpportunity(opportunityId: string): Observable<LawFirmInfo> {
    return this.http.get<LawFirmInfo>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/law-firm`
    );
  }

  getAttorneyByOpportunity(opportunityId: string): Observable<AttorneyInfo> {
    return this.http.get<AttorneyInfo>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/attorney`
    );
  }
}

export interface LawFirmInfo {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  email: string;
}

export interface AttorneyInfo {
  id: string;
  fullName: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  title: string;
  lawFirmId: string;
}

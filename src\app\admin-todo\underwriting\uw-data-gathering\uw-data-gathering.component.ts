import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  UntypedFormArray,
} from '@angular/forms';
import { MatLegacyDialog as MatDialog } from '@angular/material/legacy-dialog';
import { debounceTime, distinctUntilChanged, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import {
  ProviderEvidenceDialogComponent,
  ProviderEvidenceData,
} from '../dialogs/provider-evidence-dialog/provider-evidence-dialog.component';
import { LawfirmService } from 'src/app/services/law-firm/lawfirm.service';
import { AttorneyService } from 'src/app/services/attorney/attorney.service';
import { NotesService } from 'src/app/services/helper/notes.service';
import { AttachmentService } from 'src/app/services/helper/attachment.service';
import { Attorney } from 'src/app/model/plaintiffs';
import { NoteResponse } from 'src/app/model/notes/note-response';
import { PatientAttachmentInfo } from 'src/app/model/patients/patient-attachment-info';
import { OpportunityInsuranceInfo } from 'src/app/model/opportunity';
import { Observable } from 'rxjs';

export interface DocumentChecklistItem {
  id: string;
  name: string;
  required: boolean;
  conditionallyRequired?: boolean;
  completed: boolean;
  dateCompleted?: Date;
  notes?: string;
  category:
    | 'sales'
    | 'legal'
    | 'medical'
    | 'provider'
    | 'insurance'
    | 'investigation';
}

export interface BookInfo {
  id: string;
  bookName: string;
  bookType: 'PCA' | 'Medical';
  requestedAmount: number;
  plaintiffCount: number;
}

@Component({
  selector: 'app-uw-data-gathering',
  templateUrl: './uw-data-gathering.component.html',
  styleUrls: ['./uw-data-gathering.component.scss'],
})
export class UWDataGatheringComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  dataGatheringForm: UntypedFormGroup;
  selectedBook: BookInfo | null = null;

  // Form sections
  salesDocuments: DocumentChecklistItem[] = [];
  legalDocuments: DocumentChecklistItem[] = [];
  medicalDocuments: DocumentChecklistItem[] = [];
  providerDocuments: DocumentChecklistItem[] = [];
  insuranceDocuments: DocumentChecklistItem[] = [];
  investigationDocuments: DocumentChecklistItem[] = [];

  // Status tracking
  lastSaved: Date | null = null;
  isSaving = false;
  hasUnsavedChanges = false;

  // Sample books for selection
  availableBooks: BookInfo[] = [
    {
      id: '1',
      bookName: 'Motor Vehicle Cases - Q1 2024',
      bookType: 'PCA',
      requestedAmount: 450000,
      plaintiffCount: 15,
    },
    {
      id: '2',
      bookName: 'Medical Malpractice - December',
      bookType: 'Medical',
      requestedAmount: 320000,
      plaintiffCount: 8,
    },
    {
      id: '3',
      bookName: 'Product Liability - January',
      bookType: 'PCA',
      requestedAmount: 680000,
      plaintiffCount: 12,
    },
    {
      id: '4',
      bookName: 'Workers Comp - Q4 2023',
      bookType: 'Medical',
      requestedAmount: 280000,
      plaintiffCount: 6,
    },
    {
      id: '5',
      bookName: 'Slip and Fall Cases - February',
      bookType: 'PCA',
      requestedAmount: 385000,
      plaintiffCount: 9,
    },
  ];

  // Salesforce data
  lawFirms: any[] = [];
  attorneys: Attorney[] = [];
  sfMedicalDocuments: PatientAttachmentInfo[] = [];
  caseNotes: NoteResponse[] = [];
  insuranceInfo: OpportunityInsuranceInfo | null = null;

  // Current opportunity/book ID for Salesforce calls
  currentOpportunityId: string = '';

  constructor(
    private fb: UntypedFormBuilder,
    private dialog: MatDialog,
    private lawfirmService: LawfirmService,
    private attorneyService: AttorneyService,
    private notesService: NotesService,
    private attachmentService: AttachmentService,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.initializeDocumentChecklist();
    this.setupAutoSave();
    this.loadSalesforceData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  initializeForm(): void {
    this.dataGatheringForm = this.fb.group({
      // Book Selection
      selectedBookId: [''],

      // Sales Documents (Auto-populated from Sales team)
      businessReport: [false],
      businessSearch: [false],
      lawsuitSearch: [false],
      uccSearch: [false],

      // Legal Documents
      lawFirm: [''],
      attorney: [''],
      attorneyLOP: [false],
      attorneyLOPDetails: [''],
      plaintiffLOP: [false],
      plaintiffLOPDetails: [''],
      policeReport: [false],
      policeReportDetails: [''],

      // Medical Documents
      medicalDocumentsCount: [0],
      medicalDocumentsAvailable: [false],
      emsReport: [false],
      mriReport: [false],
      operativeReport: [false],
      doctorsReport: [false],

      // Incident & Injury Details
      incidentDescription: [''],
      liabilityDetail: [''],
      injuryDetail: [''],
      injurySeverity: [1],
      liabilityPercentage: [0],

      // Insurance Information
      decPage: [false],
      decPageVerbal: [false],
      insuranceCarrier1: [''],
      insuranceCarrier2: [''],
      // Note: insuranceLimits1, insuranceLimits2, insuranceType1, insuranceType2
      // are created by their respective field components

      // Attorney Discussion
      attorneyDiscussionNotes: [''],

      // General Notes
      notes: [''],
      aiNotesSummary: [{ value: '', disabled: true }],

      // Status
      stage: ['pending'],
      flagged: [false],
      flaggedReason: [''],
    });
  }

  initializeDocumentChecklist(): void {
    // Sales Documents (Generally gathered by Sales)
    this.salesDocuments = [
      {
        id: 'business_report',
        name: 'Business Report',
        required: false,
        completed: false,
        category: 'sales',
      },
      {
        id: 'business_search',
        name: 'Business Search',
        required: false,
        completed: false,
        category: 'sales',
      },
      {
        id: 'lawsuit_search',
        name: 'Lawsuit Search',
        required: false,
        completed: false,
        category: 'sales',
      },
      {
        id: 'ucc_search',
        name: 'UCC Search',
        required: false,
        completed: false,
        category: 'sales',
      },
    ];

    // Legal Documents
    this.legalDocuments = [
      {
        id: 'law_firm',
        name: 'Law Firm',
        required: true,
        completed: false,
        category: 'legal',
      },
      {
        id: 'attorney',
        name: 'Attorney',
        required: true,
        completed: false,
        category: 'legal',
      },
      {
        id: 'attorney_lop',
        name: 'Attorney LOP (If purchase or advance)',
        required: true,
        conditionallyRequired: true,
        completed: false,
        category: 'legal',
      },
      {
        id: 'plaintiff_lop',
        name: 'Plaintiff LOP',
        required: true,
        completed: false,
        category: 'legal',
      },
      {
        id: 'police_report',
        name: 'Police Report',
        required: true,
        completed: false,
        category: 'legal',
      },
    ];

    // Medical Documents
    this.medicalDocuments = [
      {
        id: 'medical_documents',
        name: 'Medical Documents (note # gathered, & if available)',
        required: false,
        completed: false,
        category: 'medical',
      },
      {
        id: 'ems_report',
        name: 'EMS Report',
        required: false,
        completed: false,
        category: 'medical',
      },
      {
        id: 'mri_report',
        name: 'MRI Report',
        required: false,
        completed: false,
        category: 'medical',
      },
      {
        id: 'operative_report',
        name: 'Operative Report',
        required: false,
        completed: false,
        category: 'medical',
      },
      {
        id: 'doctors_report',
        name: 'Doctors Report/Eval',
        required: false,
        completed: false,
        category: 'medical',
      },
    ];

    // Provider Documents
    this.providerDocuments = [
      {
        id: 'provider_evidence',
        name: 'Required Provider Facility-Related Evidence',
        required: true,
        completed: false,
        category: 'provider',
      },
    ];

    // Insurance Documents
    this.insuranceDocuments = [
      {
        id: 'dec_page',
        name: 'Dec Page (with verbal checkbox)',
        required: true,
        conditionallyRequired: true,
        completed: false,
        category: 'insurance',
      },
      {
        id: 'insurance_carrier_1',
        name: 'Insurance Carrier 1',
        required: true,
        completed: false,
        category: 'insurance',
      },
      {
        id: 'insurance_limits_1',
        name: 'Insurance Limits 1',
        required: true,
        completed: false,
        category: 'insurance',
      },
      {
        id: 'insurance_type_1',
        name: 'Insurance Type 1',
        required: true,
        completed: false,
        category: 'insurance',
      },
    ];

    // Investigation Documents
    this.investigationDocuments = [
      {
        id: 'incident_description',
        name: 'Incident Description & Liability Detail',
        required: true,
        completed: false,
        category: 'investigation',
      },
      {
        id: 'injury_detail',
        name: 'Injury Detail',
        required: true,
        completed: false,
        category: 'investigation',
      },
      {
        id: 'liability_percentage',
        name: 'Liability %',
        required: true,
        completed: false,
        category: 'investigation',
      },
    ];
  }

  setupAutoSave(): void {
    // Watch for book selection changes
    this.dataGatheringForm
      .get('selectedBookId')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((bookId: string) => {
        if (bookId) {
          this.onBookSelected(bookId);
        }
      });

    // Auto-save form changes
    this.dataGatheringForm.valueChanges
      .pipe(
        debounceTime(2000), // Wait 2 seconds after user stops typing
        distinctUntilChanged(),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.hasUnsavedChanges = true;
        this.autoSave();
      });
  }

  onBookSelected(bookId: string): void {
    this.selectedBook =
      this.availableBooks.find((book) => book.id === bookId) || null;
    if (this.selectedBook) {
      this.updateConditionalRequirements();

      // Update opportunity ID based on selected book
      // In a real implementation, this would come from the book data
      // For now, let's try to use a real opportunity ID if available
      this.updateOpportunityId(bookId);
    }
  }

  updateOpportunityId(bookId: string): void {
    // TODO: In real implementation, get the actual opportunity ID from the selected book
    // For now, we'll try some common opportunity ID patterns
    const possibleOpportunityIds = [
      '0063t00000YourOpportunityId', // Replace with actual pattern
      '006000000000000', // Generic Salesforce ID pattern
      bookId, // Try using book ID directly
    ];

    // Try the first available opportunity ID
    this.currentOpportunityId = possibleOpportunityIds[0];
    console.log('Updated opportunity ID to:', this.currentOpportunityId);

    // Reload case notes with new opportunity ID
    this.loadCaseNotes();
  }

  updateConditionalRequirements(): void {
    if (!this.selectedBook) return;

    // Update conditional requirements based on book type and amount
    const requestedAmount = this.selectedBook.requestedAmount;

    // Dec Page verbal requirement for over $2500
    const decPageDoc = this.insuranceDocuments.find(
      (doc) => doc.id === 'dec_page',
    );
    if (decPageDoc) {
      decPageDoc.conditionallyRequired = requestedAmount > 2500;
    }

    // Attorney LOP for purchase or advance
    const attorneyLOPDoc = this.legalDocuments.find(
      (doc) => doc.id === 'attorney_lop',
    );
    if (attorneyLOPDoc) {
      attorneyLOPDoc.conditionallyRequired =
        this.selectedBook.bookType === 'PCA';
    }
  }

  onDocumentToggle(document: DocumentChecklistItem): void {
    document.completed = !document.completed;
    document.dateCompleted = document.completed ? new Date() : undefined;
    this.hasUnsavedChanges = true;
  }

  autoSave(): void {
    if (!this.hasUnsavedChanges) return;

    this.isSaving = true;
    // Simulate API call
    setTimeout(() => {
      this.lastSaved = new Date();
      this.isSaving = false;
      this.hasUnsavedChanges = false;
      console.log(
        'Auto-saved data gathering form',
        this.dataGatheringForm.value,
      );
    }, 1000);
  }

  onInjurySeverityChange(value: number): void {
    this.dataGatheringForm.patchValue({ injurySeverity: value });
  }

  onLiabilityPercentageChange(value: number): void {
    this.dataGatheringForm.patchValue({ liabilityPercentage: value });
  }

  openProviderEvidenceDialog(): void {
    if (!this.selectedBook) {
      alert('Please select a book first');
      return;
    }

    const dialogData: ProviderEvidenceData = {
      bookId: this.selectedBook.id,
      bookName: this.selectedBook.bookName,
      existingData: null, // In real implementation, load existing data
    };

    const dialogRef = this.dialog.open(ProviderEvidenceDialogComponent, {
      width: '900px',
      maxWidth: '95vw',
      maxHeight: '90vh',
      data: dialogData,
      disableClose: false,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        console.log('Provider evidence saved:', result);

        // Update the provider document as completed
        const providerDoc = this.providerDocuments.find(
          (doc) => doc.id === 'provider_evidence',
        );
        if (providerDoc) {
          providerDoc.completed = true;
          providerDoc.dateCompleted = new Date();
          providerDoc.notes = `Updated: ${result.lastUpdated}`;
        }

        this.hasUnsavedChanges = true;
        this.autoSave();
      }
    });
  }

  generateAINoteSummary(): void {
    // Generate AI summary of notes
    const currentNotes = this.dataGatheringForm.get('notes')?.value || '';
    const summary = `AI Generated Summary (${new Date().toLocaleString()}): ${currentNotes.substring(0, 100)}...`;

    const aiNotesControl = this.dataGatheringForm.get('aiNotesSummary');
    if (aiNotesControl) {
      aiNotesControl.enable();
      aiNotesControl.setValue(summary);
      aiNotesControl.disable(); // Disable again after setting value
    }
  }

  markAsPending(): void {
    this.dataGatheringForm.patchValue({
      stage: 'pending',
      flagged: true,
      flaggedReason: 'Information missing - returned to Intake',
    });
    this.autoSave();
  }

  flagForMissingInfo(): void {
    this.dataGatheringForm.patchValue({
      flagged: true,
      flaggedReason: 'Required provider facility-related evidence missing',
    });
    this.autoSave();
  }

  getRequiredDocumentsCount(): number {
    const allDocs = [
      ...this.legalDocuments,
      ...this.medicalDocuments,
      ...this.providerDocuments,
      ...this.insuranceDocuments,
      ...this.investigationDocuments,
    ];
    return allDocs.filter((doc) => doc.required || doc.conditionallyRequired)
      .length;
  }

  getCompletedDocumentsCount(): number {
    const allDocs = [
      ...this.legalDocuments,
      ...this.medicalDocuments,
      ...this.providerDocuments,
      ...this.insuranceDocuments,
      ...this.investigationDocuments,
    ];
    return allDocs.filter(
      (doc) => doc.completed && (doc.required || doc.conditionallyRequired),
    ).length;
  }

  getCompletionPercentage(): number {
    const total = this.getRequiredDocumentsCount();
    const completed = this.getCompletedDocumentsCount();
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  }

  completeDataGathering(): void {
    // Mark as complete and ready for evaluation
    console.log('Completing data gathering for book:', this.selectedBook);
    alert('Data gathering marked as complete!');
  }

  // Salesforce Integration Methods
  loadSalesforceData(): void {
    // For now, we'll use mock opportunity ID
    // In real implementation, this would come from the selected book
    this.currentOpportunityId = 'mock-opportunity-id';

    this.loadLawFirms();
    this.loadCaseNotes();
    this.loadMedicalDocuments();
    // Insurance info will be loaded when book is selected
  }

  loadLawFirms(): void {
    // Load law firms using existing service - increase page size to get more firms
    // Use showAllFirms = true to get all firms from Salesforce
    this.attorneyService.getFirmSearch('', 500, true, true).subscribe({
      next: (firms) => {
        this.lawFirms = firms || [];
        console.log(`Loaded ${this.lawFirms.length} law firms:`, this.lawFirms);

        // Log the structure of the first firm to understand the data format
        if (this.lawFirms.length > 0) {
          console.log('First law firm structure:', this.lawFirms[0]);
        }
      },
      error: (error) => {
        console.error('Error loading law firms:', error);
      },
    });
  }

  loadAttorneys(lawFirmId: string): void {
    if (!lawFirmId) return;

    console.log('Loading attorneys for law firm ID:', lawFirmId);

    this.lawfirmService.getLawFirmAttorneys(lawFirmId).subscribe({
      next: (attorneys) => {
        this.attorneys = attorneys || [];
        console.log(
          `Loaded ${this.attorneys.length} attorneys for firm:`,
          attorneys,
        );

        // If no attorneys found, try alternative search method
        if (this.attorneys.length === 0) {
          console.log(
            'No attorneys found with getLawFirmAttorneys, trying alternative search...',
          );
          this.loadAttorneysAlternative(lawFirmId);
        }
      },
      error: (error) => {
        console.error('Error loading attorneys:', error);
        // Try alternative method on error
        this.loadAttorneysAlternative(lawFirmId);
      },
    });
  }

  loadAttorneysAlternative(lawFirmId: string): void {
    // Try using the provider attorney search as an alternative
    this.attorneyService
      .getProviderAttorneySearch(lawFirmId, '', 'Attorney', 100)
      .subscribe({
        next: (attorneys) => {
          this.attorneys = attorneys || [];
          console.log(
            `Alternative search loaded ${this.attorneys.length} attorneys:`,
            attorneys,
          );
        },
        error: (error) => {
          console.error('Alternative attorney search also failed:', error);
        },
      });
  }

  loadCaseNotes(): void {
    if (!this.currentOpportunityId) return;

    this.notesService
      .getNotesByOpportunity(this.currentOpportunityId, 50, 0)
      .subscribe({
        next: (notes) => {
          this.caseNotes = notes || [];
          console.log('Loaded case notes:', this.caseNotes);
          this.populateCaseNotesField();
        },
        error: (error) => {
          console.error('Error loading case notes:', error);
        },
      });
  }

  loadMedicalDocuments(): void {
    // This would load medical documents from Salesforce
    // For now, we'll create a placeholder method
    console.log('Loading medical documents from Salesforce...');
    // TODO: Implement when we identify the correct Salesforce endpoint
  }

  populateCaseNotesField(): void {
    if (this.caseNotes.length > 0) {
      const notesText = this.caseNotes
        .map(
          (note) =>
            `${new Date(note.lastModifiedDate).toLocaleDateString()}: ${note.title}\n${note.content}`,
        )
        .join('\n\n');

      this.dataGatheringForm.patchValue({
        attorneyDiscussionNotes: notesText,
      });

      // Make the field read-only by disabling it
      this.dataGatheringForm.get('attorneyDiscussionNotes')?.disable();
    }
  }

  onLawFirmSelected(event: any): void {
    const firmId = event?.value || event;
    console.log('Law firm selected event:', event);
    console.log('Extracted firm ID:', firmId);

    if (firmId) {
      // Find the selected firm object to get more details
      const selectedFirm = this.lawFirms.find((firm) => firm.id === firmId);
      console.log('Selected firm object:', selectedFirm);

      // Clear previous attorneys
      this.attorneys = [];

      // Load attorneys for this firm
      this.loadAttorneys(firmId);

      // Update form
      this.dataGatheringForm.patchValue({
        lawFirm: firmId,
      });

      // Mark law firm document as completed
      const lawFirmDoc = this.legalDocuments.find(
        (doc) => doc.id === 'law_firm',
      );
      if (lawFirmDoc) {
        lawFirmDoc.completed = true;
        lawFirmDoc.dateCompleted = new Date();
      }
    }
  }

  onAttorneySelected(event: any): void {
    const attorneyId = event?.value || event;
    if (attorneyId) {
      this.dataGatheringForm.patchValue({
        attorney: attorneyId,
      });

      // Mark attorney document as completed
      const attorneyDoc = this.legalDocuments.find(
        (doc) => doc.id === 'attorney',
      );
      if (attorneyDoc) {
        attorneyDoc.completed = true;
        attorneyDoc.dateCompleted = new Date();
      }
    }
  }

  checkLOPRequirements(): void {
    // Check if LOP fields exist in Salesforce
    // If not, require document upload
    const attorneyLOPExists = false; // TODO: Check Salesforce
    const plaintiffLOPExists = false; // TODO: Check Salesforce

    if (!attorneyLOPExists) {
      console.log('Attorney LOP not found in SF - require document upload');
      // TODO: Show upload requirement
    }

    if (!plaintiffLOPExists) {
      console.log('Plaintiff LOP not found in SF - require document upload');
      // TODO: Show upload requirement
    }
  }

  // Temporary method for testing with real opportunity IDs
  testWithOpportunityId(opportunityId: string): void {
    if (opportunityId && opportunityId.trim()) {
      console.log('Testing with opportunity ID:', opportunityId);
      this.currentOpportunityId = opportunityId.trim();
      this.loadCaseNotes();
    } else {
      console.log('Please enter a valid opportunity ID');
    }
  }
}

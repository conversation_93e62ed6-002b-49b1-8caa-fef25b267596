# Backend Controller Implementation Plan

## OpportunitiesController Extensions

```csharp
[HttpGet("{id}/details")]
public async Task<IActionResult> GetOpportunityDetails(string id) 
{
    var soql = SalesForceQueries.GetOpportunityDetails(id);
    var result = await _salesforceQueryService.QuerySalesforce<SF_OpportunityDetails>(soql);
    return Ok(result);
}

[HttpGet("{id}/plaintiff")]
public async Task<IActionResult> GetPlaintiffDetails(string id) 
{
    var soql = SalesForceQueries.GetPatientDetails(id);
    var result = await _salesforceQueryService.QuerySalesforce<SF_PatientDetails>(soql);
    return Ok(result);
}

[HttpGet("{id}/insurance")]
public async Task<IActionResult> GetInsuranceInfo(string id) 
{
    var soql = SalesForceQueries.GetInsuranceInfo(id);
    var result = await _salesforceQueryService.QuerySalesforce<SF_InsuranceInfo>(soql);
    return Ok(result);
}

[HttpGet("{id}/case-info")]
public async Task<IActionResult> GetCaseInformation(string id) 
{
    var soql = SalesForceQueries.GetCaseInformation(id);
    var result = await _salesforceQueryService.QuerySalesforce<SF_CaseInfo>(soql);
    return Ok(result);
}

[HttpGet("{id}/funding-records")]
public async Task<IActionResult> GetFundingRecords(string id) 
{
    var result = await _clientService.GetFundingsByOpportunity(id);
    return Ok(result);
}

[HttpGet("{id}/underwriting-metrics")]
public async Task<IActionResult> GetUnderwritingMetrics(string id) 
{
    var result = await _salesforceApexService.CallApexEndpoint<UnderwritingMetrics>(
        "getUnderwritingMetrics", 
        new { opportunityId = id }
    );
    return Ok(result);
}
```

## Required SOQL Queries (SalesForceQueries class)

```csharp
public static string GetOpportunityDetails(string opportunityId) => 
    $@"SELECT Id, Name, StageName, Amount, CloseDate, AccountId, Account.Name,
              Date_of_Injury__c, Accident_State__c, Case_Type__c, 
              Injury_Severity__c, Liability_Percentage__c,
              Medical_Limit__c, Claim_Number__c, Accident_Report_Number__c
       FROM Opportunity 
       WHERE Id = '{opportunityId}'";

public static string GetInsuranceInfo(string opportunityId) => 
    $@"SELECT Insurance_Company_I__c, Insurance_Company_II__c,
              Insurance_Limits_1__c, Insurance_Limits_2__c,
              Policy_Type_1__c, Policy_Type_2__c,
              Dec_Page__c, Dec_Page_Verbal__c
       FROM Opportunity 
       WHERE Id = '{opportunityId}'";

public static string GetCaseInformation(string opportunityId) => 
    $@"SELECT Injuries__c, Injury_Details__c, Incident_Description__c,
              Police_Report_Number__c, Police_Report_Details__c
       FROM Opportunity 
       WHERE Id = '{opportunityId}'";
```

## Data Models Required

```csharp
public class SF_OpportunityDetails
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string StageName { get; set; }
    public decimal? Amount { get; set; }
    public DateTime? CloseDate { get; set; }
    public string AccountId { get; set; }
    public SF_Account Account { get; set; }
    public DateTime? Date_of_Injury__c { get; set; }
    public string Accident_State__c { get; set; }
    public string Case_Type__c { get; set; }
    public decimal? Injury_Severity__c { get; set; }
    public decimal? Liability_Percentage__c { get; set; }
    public string Medical_Limit__c { get; set; }
    public string Claim_Number__c { get; set; }
    public string Accident_Report_Number__c { get; set; }
}

public class SF_InsuranceInfo
{
    public string Insurance_Company_I__c { get; set; }
    public string Insurance_Company_II__c { get; set; }
    public string Insurance_Limits_1__c { get; set; }
    public string Insurance_Limits_2__c { get; set; }
    public string Policy_Type_1__c { get; set; }
    public string Policy_Type_2__c { get; set; }
    public bool? Dec_Page__c { get; set; }
    public bool? Dec_Page_Verbal__c { get; set; }
}

public class SF_CaseInfo
{
    public string Injuries__c { get; set; }
    public string Injury_Details__c { get; set; }
    public string Incident_Description__c { get; set; }
    public string Police_Report_Number__c { get; set; }
    public string Police_Report_Details__c { get; set; }
}

public class UnderwritingMetrics
{
    public decimal RiskScore { get; set; }
    public string RiskLevel { get; set; }
    public decimal RecommendedAmount { get; set; }
    public List<string> RiskFactors { get; set; }
}
```

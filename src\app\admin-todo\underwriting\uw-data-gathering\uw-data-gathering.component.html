<div class="container-fluid uw-data-gathering-container">
  <!-- Header with Book Selection and Progress -->
  <div class="header-section">
    <div class="row">
      <div class="col-md-6">
        <h4>UW Data Gathering</h4>
        <div class="book-selection">
          <form [formGroup]="dataGatheringForm">
            <app-select-field
              [form]="dataGatheringForm"
              formControlName="selectedBookId"
              [items]="availableBooks"
              placeholder="Select Book"
              appearance="fill"
              descProperty="bookName"
              idProperty="id"
            ></app-select-field>
          </form>
        </div>
      </div>
      <div class="col-md-6">
        <div class="progress-section" *ngIf="selectedBook">
          <h6>Completion Progress</h6>
          <mat-progress-bar
            mode="determinate"
            [value]="getCompletionPercentage()"
            color="primary"
          >
          </mat-progress-bar>
          <div class="progress-text">
            {{ getCompletedDocumentsCount() }} of
            {{ getRequiredDocumentsCount() }} required items completed ({{
              getCompletionPercentage()
            }}%)
          </div>
        </div>

        <!-- Auto-save indicator -->
        <div class="save-status">
          <mat-icon *ngIf="isSaving" class="saving-icon">sync</mat-icon>
          <span *ngIf="lastSaved" class="last-saved">
            Last saved: {{ lastSaved | date: "short" }}
          </span>
          <span *ngIf="hasUnsavedChanges" class="unsaved-changes">
            Unsaved changes
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Book Information Display -->
  <div class="book-info-section" *ngIf="selectedBook">
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>{{ selectedBook.bookName }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="row">
          <div class="col-md-4">
            <strong>Book Type:</strong>
            <span
              class="book-type-badge"
              [ngClass]="{
                'pca-badge': selectedBook.bookType === 'PCA',
                'medical-badge': selectedBook.bookType === 'Medical'
              }"
            >
              {{ selectedBook.bookType }}
            </span>
          </div>
          <div class="col-md-4">
            <strong>Plaintiff Count:</strong> {{ selectedBook.plaintiffCount }}
          </div>
          <div class="col-md-4">
            <strong>Requested Amount:</strong>
            {{ selectedBook.requestedAmount | currency }}
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <form [formGroup]="dataGatheringForm" *ngIf="selectedBook">
    <!-- Sales Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>business</mat-icon>
            Sales Documents (Generally gathered by Sales)
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="document-checklist">
            <div *ngFor="let doc of salesDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Legal Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>gavel</mat-icon>
            Legal Documents
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-6">
              <app-select-field
                [form]="dataGatheringForm"
                formControlName="lawFirm"
                [items]="lawFirms"
                placeholder="Law Firm *"
                [required]="true"
                appearance="fill"
                [idProperty]="'id'"
                [descProperty]="'name'"
                (selectionChange)="onLawFirmSelected($event)"
              ></app-select-field>
            </div>
            <div class="col-md-6">
              <app-select-field
                [form]="dataGatheringForm"
                formControlName="attorney"
                [items]="attorneys"
                placeholder="Attorney *"
                [required]="true"
                appearance="fill"
                [idProperty]="'id'"
                [descProperty]="'name'"
                (selectionChange)="onAttorneySelected($event)"
              ></app-select-field>
            </div>
          </div>

          <!-- Attorney LOP and Plaintiff LOP Text Fields -->
          <div class="row">
            <div class="col-md-6">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="attorneyLOPDetails"
                placeholder="Attorney LOP Details * (If purchase or advance)"
                [required]="selectedBook?.bookType === 'PCA'"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
            <div class="col-md-6">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="plaintiffLOPDetails"
                placeholder="Plaintiff LOP Details *"
                [required]="true"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="policeReportDetails"
                placeholder="Police Report Details *"
                [required]="true"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of legalDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
                <span *ngIf="doc.required" class="required-asterisk">*</span>
                <span
                  *ngIf="
                    doc.conditionallyRequired &&
                    selectedBook?.bookType === 'PCA'
                  "
                  class="required-asterisk"
                  >*</span
                >
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Medical Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>medical_services</mat-icon>
            Medical Documents
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="medicalDocumentsCount"
                placeholder="Number of Medical Documents Gathered"
                type="number"
                appearance="fill"
              ></app-input-field>
            </div>
            <div class="col-md-6">
              <mat-checkbox
                formControlName="medicalDocumentsAvailable"
                [color]="'primary'"
              >
                Medical Documents Available
              </mat-checkbox>
            </div>
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of medicalDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Incident & Injury Details Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>description</mat-icon>
            Incident & Injury Details
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="incidentDescription"
                placeholder="Incident Description & Liability Detail *"
                [required]="true"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="injuryDetail"
                placeholder="Injury Detail *"
                [required]="true"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill">
                <mat-label>Injury Severity *</mat-label>
                <mat-select
                  formControlName="injurySeverity"
                  (selectionChange)="onInjurySeverityChange($event.value)"
                >
                  <mat-option value="1">1 - Minor</mat-option>
                  <mat-option value="1.5">1.5 - Minor to Moderate</mat-option>
                  <mat-option value="2">2 - Moderate</mat-option>
                  <mat-option value="2.5">2.5 - Moderate to Severe</mat-option>
                  <mat-option value="3">3 - Severe</mat-option>
                  <mat-option value="4">4 - Fatal</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill">
                <mat-label>Liability % *</mat-label>
                <input
                  matInput
                  type="number"
                  min="0"
                  max="100"
                  formControlName="liabilityPercentage"
                  (input)="
                    onLiabilityPercentageChange($any($event.target).value)
                  "
                />
                <mat-hint>Enter percentage (0-100)</mat-hint>
              </mat-form-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Insurance Information Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>security</mat-icon>
            Insurance Information
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <div class="dec-page-section">
                <mat-checkbox formControlName="decPage" [color]="'primary'">
                  Dec Page *
                  <span
                    *ngIf="selectedBook && selectedBook.requestedAmount > 2500"
                    class="required-asterisk"
                    >*</span
                  >
                </mat-checkbox>
                <mat-checkbox
                  formControlName="decPageVerbal"
                  [color]="'primary'"
                  class="ml-4"
                >
                  Verbal Confirmation
                  <span
                    *ngIf="selectedBook && selectedBook.requestedAmount > 2500"
                    class="required-asterisk"
                  >
                    (Required for amounts over $2,500)
                  </span>
                </mat-checkbox>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="insuranceCarrier1"
                placeholder="Insurance Carrier 1 *"
                [required]="true"
                appearance="fill"
              ></app-input-field>
            </div>
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="insuranceCarrier2"
                placeholder="Insurance Carrier 2"
                appearance="fill"
              ></app-input-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Limits 1 *</mat-label>
                <mat-select formControlName="insuranceLimits1">
                  <mat-option value="25/50">$25,000/$50,000</mat-option>
                  <mat-option value="50/100">$50,000/$100,000</mat-option>
                  <mat-option value="100/300">$100,000/$300,000</mat-option>
                  <mat-option value="250/500">$250,000/$500,000</mat-option>
                  <mat-option value="500/1000">$500,000/$1,000,000</mat-option>
                  <mat-option value="1000/2000"
                    >$1,000,000/$2,000,000</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Limits 2</mat-label>
                <mat-select formControlName="insuranceLimits2">
                  <mat-option value="25/50">$25,000/$50,000</mat-option>
                  <mat-option value="50/100">$50,000/$100,000</mat-option>
                  <mat-option value="100/300">$100,000/$300,000</mat-option>
                  <mat-option value="250/500">$250,000/$500,000</mat-option>
                  <mat-option value="500/1000">$500,000/$1,000,000</mat-option>
                  <mat-option value="1000/2000"
                    >$1,000,000/$2,000,000</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Type 1 *</mat-label>
                <mat-select formControlName="insuranceType1">
                  <mat-option value="auto">Auto Insurance</mat-option>
                  <mat-option value="general">General Liability</mat-option>
                  <mat-option value="professional"
                    >Professional Liability</mat-option
                  >
                  <mat-option value="umbrella">Umbrella Policy</mat-option>
                  <mat-option value="workers_comp"
                    >Workers Compensation</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Type 2</mat-label>
                <mat-select formControlName="insuranceType2">
                  <mat-option value="auto">Auto Insurance</mat-option>
                  <mat-option value="general">General Liability</mat-option>
                  <mat-option value="professional"
                    >Professional Liability</mat-option
                  >
                  <mat-option value="umbrella">Umbrella Policy</mat-option>
                  <mat-option value="workers_comp"
                    >Workers Compensation</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Provider Evidence Section -->
    <div class="section-card">
      <mat-card class="provider-evidence-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>local_hospital</mat-icon>
            Required Provider Facility-Related Evidence
            <span class="required-indicator">* Required</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div
            class="provider-evidence-warning"
            *ngIf="!providerDocuments[0]?.completed"
          >
            <mat-icon color="warn">warning</mat-icon>
            <span
              >This book is flagged - Required provider facility-related
              evidence is not in place.</span
            >
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of providerDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
                <span class="required-asterisk">*</span>
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>

          <button
            mat-raised-button
            color="primary"
            (click)="openProviderEvidenceDialog()"
            class="provider-evidence-btn"
          >
            <mat-icon>launch</mat-icon>
            View/Enter Provider Evidence Details
          </button>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Case Notes Section (Auto-populated from Salesforce) -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>note</mat-icon>
            Case Notes
          </mat-card-title>
          <mat-card-subtitle>
            Auto-populated from Messages & Notes (Plaintiffs) in Salesforce
            <br />
            <small
              >Current Opportunity ID:
              {{ currentOpportunityId || "Not set" }}</small
            >
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="row" *ngIf="caseNotes.length > 0">
            <div class="col-md-12">
              <div class="notes-display">
                <div *ngFor="let note of caseNotes" class="note-item">
                  <div class="note-header">
                    <strong>{{ note.lastModifiedDate | date: "short" }}</strong>
                    <span class="note-author" *ngIf="note.createdBy"
                      >by {{ note.createdBy }}</span
                    >
                  </div>
                  <div class="note-title">{{ note.title }}</div>
                  <div class="note-body">{{ note.content }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="row" *ngIf="caseNotes.length === 0">
            <div class="col-md-12">
              <p class="text-muted">
                No case notes found in Salesforce for this opportunity.
              </p>
              <!-- Enhanced testing input -->
              <div class="mt-3">
                <label>Test with real Opportunity ID:</label>
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    placeholder="Enter Salesforce Opportunity ID (e.g., 0063t00000...)"
                    #opportunityIdInput
                    [value]="currentTestOpportunityId"
                  />
                  <div class="input-group-append">
                    <button
                      class="btn btn-outline-secondary"
                      type="button"
                      (click)="testWithOpportunityId(opportunityIdInput.value)"
                    >
                      Test Load Notes
                    </button>
                    <button
                      class="btn btn-outline-primary"
                      type="button"
                      (click)="testLoadAllUWData(opportunityIdInput.value)"
                    >
                      Test Load All UW Data
                    </button>
                  </div>
                </div>
                <div class="mt-2">
                  <button
                    class="btn btn-success btn-sm"
                    type="button"
                    (click)="testWithRandomRealOpportunityId()"
                  >
                    🎲 Test with Random Real Opportunity ID
                  </button>
                  <small class="text-muted ml-2">
                    {{ realOpportunityIds.length }} real opportunity IDs loaded
                  </small>
                </div>
                <small class="text-muted">
                  This will test loading all UW data including opportunity
                  details, insurance info, and plaintiff details.
                </small>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="attorneyDiscussionNotes"
                placeholder="Additional notes (auto-populated from SF)"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Notes Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>note</mat-icon>
            Notes & AI Summary
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="notes"
                placeholder="General Notes (will auto-generate based on information added)"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-10">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="aiNotesSummary"
                placeholder="AI Generated Notes Summary"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
            <div class="col-md-2">
              <button
                mat-raised-button
                color="accent"
                (click)="generateAINoteSummary()"
                class="ai-summary-btn"
              >
                <mat-icon>smart_toy</mat-icon>
                Generate AI Summary
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Action Buttons Section -->
    <div class="action-section">
      <mat-card>
        <mat-card-content>
          <div class="action-buttons">
            <button
              mat-raised-button
              color="warn"
              (click)="markAsPending()"
              class="action-btn"
            >
              <mat-icon>pending</mat-icon>
              Mark as PENDING (Return to Intake)
            </button>

            <button
              mat-raised-button
              color="accent"
              (click)="flagForMissingInfo()"
              class="action-btn"
            >
              <mat-icon>flag</mat-icon>
              Flag for Missing Provider Evidence
            </button>

            <button
              mat-raised-button
              color="primary"
              [disabled]="getCompletionPercentage() < 100"
              class="action-btn"
            >
              <mat-icon>check_circle</mat-icon>
              Complete Data Gathering
            </button>
          </div>

          <!-- Status Display -->
          <div
            class="status-display"
            *ngIf="dataGatheringForm.get('flagged')?.value"
          >
            <mat-chip-list>
              <mat-chip color="warn" selected>
                <mat-icon>warning</mat-icon>
                Flagged: {{ dataGatheringForm.get("flaggedReason")?.value }}
              </mat-chip>
            </mat-chip-list>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </form>

  <!-- No Book Selected Message -->
  <div class="no-book-selected" *ngIf="!selectedBook">
    <mat-card>
      <mat-card-content>
        <div class="empty-state">
          <mat-icon>folder_open</mat-icon>
          <h5>No Book Selected</h5>
          <p>
            Please select a book from the dropdown above to begin data
            gathering.
          </p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

<div class="container-fluid uw-data-gathering-container">
  <!-- Header with Book Selection and Progress -->
  <div class="header-section">
    <div class="row">
      <div class="col-md-6">
        <h4>UW Data Gathering</h4>
        <div class="book-selection">
          <form [formGroup]="dataGatheringForm">
            <app-select-field
              [form]="dataGatheringForm"
              formControlName="selectedBookId"
              [items]="availableBooks"
              placeholder="Select AR Book (Accounts Receivable)"
              appearance="fill"
              descProperty="name"
              idProperty="id"
            ></app-select-field>
          </form>

          <!-- Backend connectivity test button -->
          <div class="row" style="margin-top: 10px">
            <div class="col-md-12">
              <button
                mat-raised-button
                color="accent"
                (click)="testBackendConnectivity()"
                class="test-button"
              >
                🔗 Test Backend Connectivity
              </button>
              <span style="margin-left: 10px; font-size: 12px; color: #666">
                Click to test real Salesforce AR Book connection
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="progress-section" *ngIf="selectedBook">
          <h6>Completion Progress</h6>

          <!-- Enhanced Book-Level Progress -->
          <div *ngIf="bookLevelRequirements.length > 0; else legacyProgress" class="book-progress">
            <!-- Overall Progress -->
            <div class="overall-progress">
              <mat-progress-bar
                mode="determinate"
                [value]="getCompletionPercentage()"
                color="primary"
              >
              </mat-progress-bar>
              <div class="progress-text">
                Overall Progress: {{ getCompletionPercentage() }}%
                <span *ngIf="getBookCompletionSummary().overallCompletion.readyForApproval" class="ready-badge">
                  ✓ Ready for Approval
                </span>
              </div>
            </div>

            <!-- Book-Level Progress -->
            <div class="book-level-progress">
              <small class="progress-label">Book-Level Requirements:</small>
              <mat-progress-bar
                mode="determinate"
                [value]="getBookCompletionSummary().bookLevelCompletion.percentage"
                color="accent"
                class="mini-progress"
              >
              </mat-progress-bar>
              <small class="progress-text">
                {{ getBookCompletionSummary().bookLevelCompletion.completed }} of
                {{ getBookCompletionSummary().bookLevelCompletion.total }} completed
                ({{ getBookCompletionSummary().bookLevelCompletion.percentage }}%)
              </small>
            </div>

            <!-- Funding-Level Progress -->
            <div class="funding-level-progress">
              <small class="progress-label">
                Funding-Level Requirements:
                <span *ngIf="samplingStrategy" class="sampling-info">
                  ({{ getBookCompletionSummary().fundingLevelCompletion.requiredForReview }} sampled for review)
                </span>
              </small>
              <mat-progress-bar
                mode="determinate"
                [value]="getBookCompletionSummary().fundingLevelCompletion.percentage"
                color="warn"
                class="mini-progress"
              >
              </mat-progress-bar>
              <small class="progress-text">
                {{ getBookCompletionSummary().fundingLevelCompletion.completedFundings }} of
                {{ getBookCompletionSummary().fundingLevelCompletion.requiredForReview }} fundings completed
                ({{ getBookCompletionSummary().fundingLevelCompletion.percentage }}%)
              </small>
            </div>

            <!-- Sampling Strategy Info -->
            <div *ngIf="samplingStrategy" class="sampling-strategy">
              <mat-icon class="info-icon">info</mat-icon>
              <small class="sampling-text">{{ samplingStrategy.rationale }}</small>
            </div>
          </div>

          <!-- Legacy Progress (fallback) -->
          <ng-template #legacyProgress>
            <mat-progress-bar
              mode="determinate"
              [value]="getCompletionPercentage()"
              color="primary"
            >
            </mat-progress-bar>
            <div class="progress-text">
              {{ getCompletedDocumentsCount() }} of
              {{ getRequiredDocumentsCount() }} required items completed ({{
                getCompletionPercentage()
              }}%)
            </div>
          </ng-template>
        </div>

        <!-- Auto-save indicator -->
        <div class="save-status">
          <mat-icon *ngIf="isSaving" class="saving-icon">sync</mat-icon>
          <span *ngIf="lastSaved" class="last-saved">
            Last saved: {{ lastSaved | date: "short" }}
          </span>
          <span *ngIf="hasUnsavedChanges" class="unsaved-changes">
            Unsaved changes
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Book Information Display -->
  <div class="book-info-section" *ngIf="selectedBook">
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>{{ selectedBook.name }}</mat-card-title>
        <mat-card-subtitle
          >AR Book ({{ selectedBook.arType }})</mat-card-subtitle
        >
      </mat-card-header>
      <mat-card-content>
        <div class="row">
          <div class="col-md-3">
            <strong>Status:</strong>
            <span class="book-type-badge">
              {{ selectedBook.status || "N/A" }}
            </span>
          </div>
          <div class="col-md-3">
            <strong>AR Type:</strong>
            {{ selectedBook.arType || "N/A" }}
          </div>
          <div class="col-md-3">
            <strong>Total Amount:</strong>
            {{ selectedBook.totalAmount | currency }}
          </div>
          <div class="col-md-3">
            <strong>Funding Count:</strong>
            {{ selectedBook.fundingCount }}
          </div>
        </div>
        <div class="row mt-2">
          <div class="col-md-4">
            <strong>Account:</strong> {{ selectedBook.accountName || "N/A" }}
          </div>
          <div class="col-md-4">
            <strong>Month/Year:</strong> {{ selectedBook.month }}/{{
              selectedBook.year
            }}
          </div>
          <div class="col-md-4">
            <strong>Created:</strong>
            {{
              selectedBook.createdDate
                ? (selectedBook.createdDate | date: "shortDate")
                : "N/A"
            }}
          </div>
        </div>

        <!-- Enhanced Fundings Section with Pagination -->
        <div class="row mt-3" *ngIf="selectedBookFundings.length > 0">
          <div class="col-12">
            <div class="funding-section">
              <div class="funding-header">
                <h6>
                  Fundings in this Book ({{ selectedBookFundings.length }}):
                </h6>

                <!-- Search and Filter Controls -->
                <div class="funding-controls">
                  <div class="row">
                    <div class="col-md-6">
                      <mat-form-field
                        appearance="outline"
                        class="funding-search"
                      >
                        <mat-label>Search fundings</mat-label>
                        <input
                          matInput
                          [(ngModel)]="fundingSearchTerm"
                          (ngModelChange)="onFundingSearchChange($event)"
                          placeholder="Search by plaintiff, law firm, attorney..."
                        />
                        <mat-icon matSuffix>search</mat-icon>
                      </mat-form-field>
                    </div>
                    <div class="col-md-4">
                      <mat-form-field
                        appearance="outline"
                        class="funding-filter"
                      >
                        <mat-label>Filter by type</mat-label>
                        <mat-select
                          [value]="fundingFilterType"
                          (selectionChange)="
                            onFundingFilterChange($event.value)
                          "
                        >
                          <mat-option value="">All Types</mat-option>
                          <mat-option
                            *ngFor="let type of getUniqueFundingTypes()"
                            [value]="type"
                          >
                            {{ type }}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    <div class="col-md-2">
                      <mat-form-field
                        appearance="outline"
                        class="page-size-select"
                      >
                        <mat-label>Per page</mat-label>
                        <mat-select
                          [value]="fundingPageSize"
                          (selectionChange)="
                            fundingPageSize = $event.value;
                            filterAndPaginateFundings()
                          "
                        >
                          <mat-option [value]="5">5</mat-option>
                          <mat-option [value]="10">10</mat-option>
                          <mat-option [value]="25">25</mat-option>
                          <mat-option [value]="50">50</mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Funding Results Summary -->
              <div
                class="funding-summary"
                *ngIf="filteredFundings.length !== selectedBookFundings.length"
              >
                <small class="text-muted">
                  Showing {{ filteredFundings.length }} of
                  {{ selectedBookFundings.length }} fundings
                  <span *ngIf="fundingSearchTerm"
                    >(filtered by "{{ fundingSearchTerm }}")</span
                  >
                  <span *ngIf="fundingFilterType"
                    >(filtered by type "{{ fundingFilterType }}")</span
                  >
                </small>
              </div>

              <!-- Funding List -->
              <div class="funding-list" *ngIf="paginatedFundings.length > 0">
                <div
                  *ngFor="let funding of paginatedFundings; let i = index"
                  class="funding-item"
                  [class.funding-item-even]="i % 2 === 0"
                >
                  <div class="funding-main-info">
                    <strong>{{
                      funding.plaintiffName || "Unknown Plaintiff"
                    }}</strong>
                    <span class="funding-amount">{{
                      funding.amount | currency
                    }}</span>
                  </div>
                  <div class="funding-details">
                    <span class="funding-type">{{
                      funding.type || "General"
                    }}</span>
                    <span class="funding-separator">•</span>
                    <span class="funding-law-firm">{{
                      funding.lawFirm || "Unknown Firm"
                    }}</span>
                    <span class="funding-separator" *ngIf="funding.attorney"
                      >•</span
                    >
                    <span class="funding-attorney" *ngIf="funding.attorney">{{
                      funding.attorney
                    }}</span>
                  </div>
                  <div class="funding-meta">
                    <small class="text-muted">
                      ID: {{ funding.id }} | Created:
                      {{ funding.createdDate | date: "short" }}
                    </small>
                  </div>
                </div>
              </div>

              <!-- No Results Message -->
              <div
                class="no-fundings"
                *ngIf="
                  paginatedFundings.length === 0 &&
                  filteredFundings.length === 0
                "
              >
                <p class="text-muted">
                  No fundings found matching your search criteria.
                </p>
                <button
                  mat-button
                  color="primary"
                  (click)="
                    fundingSearchTerm = '';
                    fundingFilterType = '';
                    filterAndPaginateFundings()
                  "
                >
                  Clear Filters
                </button>
              </div>

              <!-- Pagination Controls -->
              <div class="funding-pagination" *ngIf="totalFundingPages > 1">
                <div class="pagination-info">
                  <small class="text-muted">
                    Page {{ fundingCurrentPage + 1 }} of
                    {{ totalFundingPages }} ({{ filteredFundings.length }} total
                    items)
                  </small>
                </div>
                <div class="pagination-controls">
                  <button
                    mat-icon-button
                    [disabled]="fundingCurrentPage === 0"
                    (click)="onFundingPageChange(0)"
                    matTooltip="First page"
                  >
                    <mat-icon>first_page</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    [disabled]="fundingCurrentPage === 0"
                    (click)="onFundingPageChange(fundingCurrentPage - 1)"
                    matTooltip="Previous page"
                  >
                    <mat-icon>chevron_left</mat-icon>
                  </button>

                  <button
                    *ngFor="let page of getFundingPageNumbers()"
                    mat-button
                    [color]="page === fundingCurrentPage ? 'primary' : ''"
                    [class.current-page]="page === fundingCurrentPage"
                    (click)="onFundingPageChange(page)"
                  >
                    {{ page + 1 }}
                  </button>

                  <button
                    mat-icon-button
                    [disabled]="fundingCurrentPage === totalFundingPages - 1"
                    (click)="onFundingPageChange(fundingCurrentPage + 1)"
                    matTooltip="Next page"
                  >
                    <mat-icon>chevron_right</mat-icon>
                  </button>
                  <button
                    mat-icon-button
                    [disabled]="fundingCurrentPage === totalFundingPages - 1"
                    (click)="onFundingPageChange(totalFundingPages - 1)"
                    matTooltip="Last page"
                  >
                    <mat-icon>last_page</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <form [formGroup]="dataGatheringForm" *ngIf="selectedBook">
    <!-- Sales Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>business</mat-icon>
            Sales Documents (Generally gathered by Sales)
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="document-checklist">
            <div *ngFor="let doc of salesDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Legal Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>gavel</mat-icon>
            Legal Documents
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-6">
              <app-firm-search-field
                [form]="dataGatheringForm"
                ctrlName="lawFirm"
                placeholder="Search Law Firm *"
                [clearEnabled]="true"
                (firmSelected)="onLawFirmSelected($event)"
                (clearFirm)="onLawFirmCleared()"
                (optionsDataUpdated)="onLawFirmOptionsUpdated()"
                appearance="fill"
              ></app-firm-search-field>
            </div>
            <div class="col-md-6">
              <app-select-field
                [form]="dataGatheringForm"
                formControlName="attorney"
                [items]="attorneys"
                placeholder="Attorney *"
                [required]="true"
                appearance="fill"
                [idProperty]="'id'"
                [descProperty]="'fullName'"
                (selectionChange)="onAttorneySelected($event)"
              ></app-select-field>
            </div>
          </div>

          <!-- Attorney LOP and Plaintiff LOP Text Fields -->
          <div class="row">
            <div class="col-md-6">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="attorneyLOPDetails"
                placeholder="Attorney LOP Details * (If purchase or advance)"
                [required]="
                  selectedBook?.arType?.toLowerCase().includes('advance') ||
                  selectedBook?.arType?.toLowerCase().includes('pca')
                "
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
            <div class="col-md-6">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="plaintiffLOPDetails"
                placeholder="Plaintiff LOP Details *"
                [required]="true"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="policeReportDetails"
                placeholder="Police Report Details *"
                [required]="true"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of legalDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
                <span *ngIf="doc.required" class="required-asterisk">*</span>
                <span
                  *ngIf="
                    doc.conditionallyRequired &&
                    (selectedBook?.arType?.toLowerCase().includes('advance') ||
                      selectedBook?.arType?.toLowerCase().includes('pca'))
                  "
                  class="required-asterisk"
                  >*</span
                >
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Medical Documents Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>medical_services</mat-icon>
            Medical Documents
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="medicalDocumentsCount"
                placeholder="Number of Medical Documents Gathered"
                type="number"
                appearance="fill"
              ></app-input-field>
            </div>
            <div class="col-md-6">
              <mat-checkbox
                formControlName="medicalDocumentsAvailable"
                [color]="'primary'"
              >
                Medical Documents Available
              </mat-checkbox>
            </div>
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of medicalDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Incident & Injury Details Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>description</mat-icon>
            Incident & Injury Details
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="incidentDescription"
                placeholder="Incident Description & Liability Detail *"
                [required]="true"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="injuryDetail"
                placeholder="Injury Detail *"
                [required]="true"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill">
                <mat-label>Injury Severity *</mat-label>
                <mat-select
                  formControlName="injurySeverity"
                  (selectionChange)="onInjurySeverityChange($event.value)"
                >
                  <mat-option value="1">1 - Minor</mat-option>
                  <mat-option value="1.5">1.5 - Minor to Moderate</mat-option>
                  <mat-option value="2">2 - Moderate</mat-option>
                  <mat-option value="2.5">2.5 - Moderate to Severe</mat-option>
                  <mat-option value="3">3 - Severe</mat-option>
                  <mat-option value="4">4 - Fatal</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill">
                <mat-label>Liability % *</mat-label>
                <input
                  matInput
                  type="number"
                  min="0"
                  max="100"
                  formControlName="liabilityPercentage"
                  (input)="
                    onLiabilityPercentageChange($any($event.target).value)
                  "
                />
                <mat-hint>Enter percentage (0-100)</mat-hint>
              </mat-form-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Insurance Information Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>security</mat-icon>
            Insurance Information
            <span class="required-indicator">* Required Fields</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <div class="dec-page-section">
                <mat-checkbox formControlName="decPage" [color]="'primary'">
                  Dec Page *
                  <span
                    *ngIf="selectedBook && selectedBook.totalAmount > 2500"
                    class="required-asterisk"
                    >*</span
                  >
                </mat-checkbox>
                <mat-checkbox
                  formControlName="decPageVerbal"
                  [color]="'primary'"
                  class="ml-4"
                >
                  Verbal Confirmation
                  <span
                    *ngIf="selectedBook && selectedBook.totalAmount > 2500"
                    class="required-asterisk"
                  >
                    (Required for amounts over $2,500)
                  </span>
                </mat-checkbox>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="insuranceCarrier1"
                placeholder="Insurance Carrier 1 *"
                [required]="true"
                appearance="fill"
              ></app-input-field>
            </div>
            <div class="col-md-6">
              <app-input-field
                [form]="dataGatheringForm"
                formControlName="insuranceCarrier2"
                placeholder="Insurance Carrier 2"
                appearance="fill"
              ></app-input-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Limits 1 *</mat-label>
                <mat-select formControlName="insuranceLimits1">
                  <mat-option value="25/50">$25,000/$50,000</mat-option>
                  <mat-option value="50/100">$50,000/$100,000</mat-option>
                  <mat-option value="100/300">$100,000/$300,000</mat-option>
                  <mat-option value="250/500">$250,000/$500,000</mat-option>
                  <mat-option value="500/1000">$500,000/$1,000,000</mat-option>
                  <mat-option value="1000/2000"
                    >$1,000,000/$2,000,000</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Limits 2</mat-label>
                <mat-select formControlName="insuranceLimits2">
                  <mat-option value="25/50">$25,000/$50,000</mat-option>
                  <mat-option value="50/100">$50,000/$100,000</mat-option>
                  <mat-option value="100/300">$100,000/$300,000</mat-option>
                  <mat-option value="250/500">$250,000/$500,000</mat-option>
                  <mat-option value="500/1000">$500,000/$1,000,000</mat-option>
                  <mat-option value="1000/2000"
                    >$1,000,000/$2,000,000</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Type 1 *</mat-label>
                <mat-select formControlName="insuranceType1">
                  <mat-option value="auto">Auto Insurance</mat-option>
                  <mat-option value="general">General Liability</mat-option>
                  <mat-option value="professional"
                    >Professional Liability</mat-option
                  >
                  <mat-option value="umbrella">Umbrella Policy</mat-option>
                  <mat-option value="workers_comp"
                    >Workers Compensation</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="col-md-6">
              <mat-form-field appearance="fill" class="w-100">
                <mat-label>Insurance Type 2</mat-label>
                <mat-select formControlName="insuranceType2">
                  <mat-option value="auto">Auto Insurance</mat-option>
                  <mat-option value="general">General Liability</mat-option>
                  <mat-option value="professional"
                    >Professional Liability</mat-option
                  >
                  <mat-option value="umbrella">Umbrella Policy</mat-option>
                  <mat-option value="workers_comp"
                    >Workers Compensation</mat-option
                  >
                  <mat-option value="other">Other</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Provider Evidence Section -->
    <div class="section-card">
      <mat-card class="provider-evidence-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>local_hospital</mat-icon>
            Required Provider Facility-Related Evidence
            <span class="required-indicator">* Required</span>
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div
            class="provider-evidence-warning"
            *ngIf="!providerDocuments[0]?.completed"
          >
            <mat-icon color="warn">warning</mat-icon>
            <span
              >This book is flagged - Required provider facility-related
              evidence is not in place.</span
            >
          </div>

          <div class="document-checklist">
            <div *ngFor="let doc of providerDocuments" class="document-item">
              <mat-checkbox
                [checked]="doc.completed"
                (change)="onDocumentToggle(doc)"
                [color]="'primary'"
              >
                {{ doc.name }}
                <span class="required-asterisk">*</span>
              </mat-checkbox>
              <mat-icon *ngIf="doc.completed" class="check-icon"
                >check_circle</mat-icon
              >
            </div>
          </div>

          <button
            mat-raised-button
            color="primary"
            (click)="openProviderEvidenceDialog()"
            class="provider-evidence-btn"
          >
            <mat-icon>launch</mat-icon>
            View/Enter Provider Evidence Details
          </button>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Messages & Notes Section - Using Proven Search Plaintiffs Components -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>message</mat-icon>
            Messages & Notes
          </mat-card-title>
          <mat-card-subtitle>
            Integrated plaintiff communication system from Search Plaintiffs
            <br />
            <small
              >Current Opportunity ID:
              {{ currentOpportunityId || "Not set" }}</small
            >
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <!-- Use the proven working notes system from Search Plaintiffs -->
          <div *ngIf="currentOpportunityId" class="notes-integration">
            <div class="mb-3">
              <div class="alert alert-success">
                <strong>✅ Notes System Active</strong><br />
                Loaded for Opportunity ID:
                <code>{{ currentOpportunityId }}</code>
                <button
                  class="btn btn-sm btn-outline-secondary ml-2"
                  (click)="clearOpportunityId()"
                >
                  Change ID
                </button>
              </div>
            </div>
            <app-opportunity-comments-tablist
              [patientId]="currentOpportunityId"
              [lawFirmAccountSalesForceId]="selectedBook?.accountId || ''"
            ></app-opportunity-comments-tablist>
          </div>

          <!-- Testing input when no opportunity ID is set -->
          <div *ngIf="!currentOpportunityId" class="notes-fallback">
            <div class="alert alert-info">
              <strong>ℹ️ Enter Opportunity ID to Load Notes System</strong>
              <div class="mt-3">
                <div class="input-group">
                  <input
                    type="text"
                    class="form-control"
                    placeholder="Enter Salesforce Opportunity ID (e.g., 0068Y00001MRrygQAD)"
                    #opportunityIdInput
                    [value]="currentTestOpportunityId"
                  />
                  <div class="input-group-append">
                    <button
                      class="btn btn-primary"
                      type="button"
                      (click)="testWithOpportunityId(opportunityIdInput.value)"
                    >
                      Load Notes System
                    </button>
                    <button
                      class="btn btn-outline-success"
                      type="button"
                      (click)="testWithRandomRealOpportunityId()"
                    >
                      🎲 Random ID
                    </button>
                  </div>
                </div>
                <small class="text-muted mt-1">
                  {{ realOpportunityIds.length }} real opportunity IDs available
                  for testing
                </small>
              </div>

              <!-- Law Firm API Testing -->
              <div class="mt-3 pt-3 border-top">
                <h6>🔧 Law Firm API Testing</h6>
                <p class="text-muted small">
                  Debug the law firm search issue (should find all 213 firms):
                </p>
                <button
                  class="btn btn-outline-warning btn-sm"
                  type="button"
                  (click)="testLawFirmAPI()"
                >
                  🧪 Test Law Firm API
                </button>
                <small class="text-muted ml-2">
                  Check console for API response details
                </small>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="attorneyDiscussionNotes"
                placeholder="Additional notes (auto-populated from SF)"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Notes Section -->
    <div class="section-card">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>note</mat-icon>
            Notes & AI Summary
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="row">
            <div class="col-md-12">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="notes"
                placeholder="General Notes (will auto-generate based on information added)"
                [rows]="4"
                appearance="fill"
              ></app-text-area-field>
            </div>
          </div>

          <div class="row">
            <div class="col-md-10">
              <app-text-area-field
                [form]="dataGatheringForm"
                formControlName="aiNotesSummary"
                placeholder="AI Generated Notes Summary"
                [rows]="3"
                appearance="fill"
              ></app-text-area-field>
            </div>
            <div class="col-md-2">
              <button
                mat-raised-button
                color="accent"
                (click)="generateAINoteSummary()"
                class="ai-summary-btn"
              >
                <mat-icon>smart_toy</mat-icon>
                Generate AI Summary
              </button>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Action Buttons Section -->
    <div class="action-section">
      <mat-card>
        <mat-card-content>
          <div class="action-buttons">
            <button
              mat-raised-button
              color="warn"
              (click)="markAsPending()"
              class="action-btn"
            >
              <mat-icon>pending</mat-icon>
              Mark as PENDING (Return to Intake)
            </button>

            <button
              mat-raised-button
              color="accent"
              (click)="flagForMissingInfo()"
              class="action-btn"
            >
              <mat-icon>flag</mat-icon>
              Flag for Missing Provider Evidence
            </button>

            <button
              mat-raised-button
              color="primary"
              [disabled]="getCompletionPercentage() < 100"
              class="action-btn"
            >
              <mat-icon>check_circle</mat-icon>
              Complete Data Gathering
            </button>
          </div>

          <!-- Status Display -->
          <div
            class="status-display"
            *ngIf="dataGatheringForm.get('flagged')?.value"
          >
            <mat-chip-list>
              <mat-chip color="warn" selected>
                <mat-icon>warning</mat-icon>
                Flagged: {{ dataGatheringForm.get("flaggedReason")?.value }}
              </mat-chip>
            </mat-chip-list>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </form>

  <!-- No Book Selected Message -->
  <div class="no-book-selected" *ngIf="!selectedBook">
    <mat-card>
      <mat-card-content>
        <div class="empty-state">
          <mat-icon>folder_open</mat-icon>
          <h5>No Book Selected</h5>
          <p>
            Please select a book from the dropdown above to begin data
            gathering.
          </p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>

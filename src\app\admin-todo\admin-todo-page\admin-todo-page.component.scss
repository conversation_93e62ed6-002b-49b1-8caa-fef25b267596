$sidebar-expanded-width: 220px;
$sidebar-collapsed-width: 56px;
$transition-duration: 0.3s;
$transition-timing: cubic-bezier(0.4, 0, 0.2, 1);

:host {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 5em);
  width: 100%;
  overflow: hidden;
}

.content-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.drawer-container {
  flex: 1;
  min-height: 0;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 0; /* Ensure the container has a stacking context */
  transition: all $transition-duration $transition-timing;
  display: flex; /* Ensure flexbox layout */

  &.container-collapsed {
    .mat-drawer-content {
      margin-left: $sidebar-collapsed-width !important;
    }
  }
}

.sidebar-drawer {
  width: $sidebar-expanded-width;
  min-width: $sidebar-expanded-width;
  border-right: 2px solid;
  border-right-color: var(--color-primary);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  transition: all $transition-duration $transition-timing;
  will-change: width, min-width, transform;
  position: relative;
  z-index: 1;
  transform: translateZ(
    0
  ); /* Create a new stacking context to prevent z-index issues */

  .sidebar-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .last-activity-container {
    margin-top: auto;
    padding: 0 0 0 0;
    max-height: 400px;
    transition: all $transition-duration $transition-timing;

    &.collapsed {
      opacity: 0;
      height: 0;
      overflow: hidden;
    }
  }
}

.sidebar-drawer-collapsed {
  width: $sidebar-collapsed-width !important;
  min-width: $sidebar-collapsed-width !important;
  transition: all $transition-duration $transition-timing;
  transform: translateZ(0); /* Force hardware acceleration */
}

::ng-deep #admin-todo-page {
  .mat-drawer-container {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    z-index: 0;
  }

  .mat-drawer {
    height: 100%;
    transition: all $transition-duration $transition-timing;
    will-change: width, min-width, transform;
    position: relative;
    z-index: 1;
    border-right: none; /* Remove default border */
  }

  .mat-drawer-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;
    transition: all $transition-duration $transition-timing;
    will-change: margin-left, width, transform;
    position: relative;
    z-index: 0;
    margin-left: 0 !important;
    padding-left: 0 !important;
  }

  .mat-drawer-inner-container {
    overflow: hidden !important;
    height: 100%;
    width: 100%; /* Ensure full width */
  }

  /* Fix for z-index issues when toggling */
  .mat-drawer-backdrop {
    position: absolute;
    z-index: -1;
  }

  .mat-drawer-container.mat-drawer-container-has-open {
    width: 100%;
  }
}

.drawer-content {
  padding: 2em 0 2em 2em;
  background-color: var(--color-content-bg);
  overflow: auto;
  transition: all $transition-duration $transition-timing;
  will-change: margin-left, width, transform;
  margin-left: 0 !important;
  box-sizing: border-box;
  width: calc(100vw - $sidebar-expanded-width) !important;
  transform: translateZ(0); /* Force hardware acceleration */

  .container-collapsed & {
    width: calc(100vw - $sidebar-collapsed-width) !important;
  }
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%; /* Ensure full width */
  height: 100%; /* Ensure full height */
  margin-top: 0; /* Ensure no negative margin */
  app-search-plaintiff,
  app-reduction-request-todo,
  app-payoff-approval-todo,
  app-data-manual-review-todo,
  app-underwriting-page {
    display: block;
    flex: 1;
    width: 100%; /* Ensure components take full width */
  }
}

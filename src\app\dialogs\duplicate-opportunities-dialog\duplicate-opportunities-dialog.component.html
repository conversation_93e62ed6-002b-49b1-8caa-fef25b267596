<div>
  <h1 mat-dialog-title>Potential Duplicate Opportunities</h1>

  <div mat-dialog-content>
    <div *ngIf="confidentMatches.length > 0" class="confident-matches-section">
      <h2 class="section-title" style="color: white">Exact Matches</h2>
      <p class="section-description" style="color: white">
        These are exact matches on name, date of birth, and date of injury with
        the same law firm.
      </p>
      <app-mat-table-template
        [data]="confidentMatches"
        [dataColumns]="columns"
        [itemIdKey]="'patientId'"
        [selectable]="false"
        [multipleSelection]="true"
        [searchEnabled]="false"
        [actionIcon]="'open_in_new'"
        [actionIconTooltip]="'View Opportunity'"
        [actionIconEnabled]="true"
        [actionIdKey]="'patientId'"
        (actionIconClicked)="openOpportunity($event)"
        (selectedItems)="onConfidentMatchesSelected($event)"
        (selectedRow)="onOpportunitySelected($event)"
      >
      </app-mat-table-template>
    </div>

    <div *ngIf="possibleMatches.length > 0" class="possible-matches-section">
      <h2 class="section-title mt-4" style="color: white">Possible Matches</h2>
      <p class="section-description" style="color: white">
        These are possible matches based on partial data like name, date of
        birth, or date of injury.
      </p>
      <app-mat-table-template
        [data]="possibleMatches"
        [dataColumns]="possibleColumns"
        [itemIdKey]="'patientId'"
        [selectable]="false"
        [multipleSelection]="true"
        [searchEnabled]="false"
        [actionIcon]="'open_in_new'"
        [actionIconTooltip]="'View Opportunity'"
        [actionIconEnabled]="true"
        [actionIdKey]="'patientId'"
        (actionIconClicked)="openOpportunity($event)"
        (selectedItems)="onPossibleMatchesSelected($event)"
        (selectedRow)="onOpportunitySelected($event)"
      >
      </app-mat-table-template>
    </div>
    <div *ngIf="selectedOpportunities.length > 0" class="selected-count">
      {{ selectedOpportunities.length }} opportunities selected
    </div>
    <div
      *ngIf="confidentMatches.length === 0 && possibleMatches.length === 0"
      class="no-matches"
    >
      <p>No potential duplicate opportunities found.</p>
    </div>
  </div>

  <div mat-dialog-actions align="center">
    <button
      *ngIf="selectedOpportunities.length > 0"
      mat-flat-button
      class="primary-btn"
      color="accent"
      (click)="onSubmitSelectedOpportunities()"
    >
      Create Reduction Request for Selected
    </button>
    <button
      mat-flat-button
      class="primary-btn"
      color="accent"
      mat-dialog-close
      cdkFocusInitial
    >
      Close
    </button>
  </div>
</div>

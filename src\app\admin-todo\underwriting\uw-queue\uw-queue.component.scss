.uw-queue-container {
  height: 100%;
  padding: 0;
}

.filter-section {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
}

.filter-row-1,
.filter-row-2 {
  margin-bottom: 10px;
}

.toggle-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  text-align: center;
}

.clear-filters-btn {
  margin-top: 10px;
}

.results-section {
  .books-table {
    width: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;

    .mat-header-cell {
      background-color: var(--color-primary);
      color: white;
      font-weight: 600;
      font-size: 14px;
    }

    .book-row {
      border-bottom: 1px solid #e0e0e0;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .book-name-cell {
      .book-name {
        font-weight: 500;
        color: var(--color-primary);
      }
    }

    .book-type-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;

      &.pca-badge {
        background-color: #e3f2fd;
        color: #1976d2;
      }

      &.medical-badge {
        background-color: #f3e5f5;
        color: #7b1fa2;
      }
    }

    .action-btn {
      margin-right: 8px;
      font-size: 12px;
      padding: 6px 12px;
    }
  }
}

.expanded-content {
  background-color: #fafafa;

  .plaintiffs-section {
    padding: 20px;

    h5 {
      margin-bottom: 15px;
      color: var(--color-primary);
      font-weight: 600;
    }

    .plaintiffs-table {
      width: 100%;
      margin-top: 10px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border-radius: 4px;

      .mat-header-cell {
        background-color: #2196f3;
        color: white;
        font-weight: 500;
        font-size: 13px;
      }

      .plaintiff-row {
        &:hover {
          background-color: #f0f0f0;
        }
      }

      .mat-cell,
      .mat-header-cell {
        padding: 8px 16px;
      }
    }
  }
}

// Button toggle styles
mat-button-toggle-group {
  border-radius: 4px;
  overflow: hidden;

  mat-button-toggle {
    border: none;

    &.mat-button-toggle-checked {
      background-color: var(--color-accent);
      color: white;
    }
  }
}

// Form field spacing
.col-md-3 {
  padding-right: 15px;

  &:last-child {
    padding-right: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .col-md-3 {
    margin-bottom: 15px;
  }

  .action-btn {
    width: 100%;
    margin-bottom: 8px;
    margin-right: 0 !important;
  }

  .books-table,
  .plaintiffs-table {
    font-size: 12px;
  }
}

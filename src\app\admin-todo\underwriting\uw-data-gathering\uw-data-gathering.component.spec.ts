import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatLegacyDialogModule as MatDialogModule } from '@angular/material/legacy-dialog';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';

import { UWDataGatheringComponent } from './uw-data-gathering.component';
import { LawfirmService } from 'src/app/services/law-firm/lawfirm.service';
import { AttorneyService } from 'src/app/services/attorney/attorney.service';
import { NotesService } from 'src/app/services/helper/notes.service';
import { AttachmentService } from 'src/app/services/helper/attachment.service';

describe('UWDataGatheringComponent', () => {
  let component: UWDataGatheringComponent;
  let fixture: ComponentFixture<UWDataGatheringComponent>;
  let mockLawfirmService: jasmine.SpyObj<LawfirmService>;
  let mockAttorneyService: jasmine.SpyObj<AttorneyService>;
  let mockNotesService: jasmine.SpyObj<NotesService>;
  let mockAttachmentService: jasmine.SpyObj<AttachmentService>;

  beforeEach(async () => {
    // Create spy objects
    mockLawfirmService = jasmine.createSpyObj('LawfirmService', [
      'getLawFirmAttorneys',
    ]);
    mockAttorneyService = jasmine.createSpyObj('AttorneyService', [
      'getFirmSearch',
      'getProviderAttorneySearch',
    ]);
    mockNotesService = jasmine.createSpyObj('NotesService', [
      'getNotesByOpportunity',
    ]);
    mockAttachmentService = jasmine.createSpyObj('AttachmentService', [
      'uploadAttachment',
    ]);

    // Setup default return values
    mockAttorneyService.getFirmSearch.and.returnValue(of([]));
    mockAttorneyService.getProviderAttorneySearch.and.returnValue(of([]));
    mockLawfirmService.getLawFirmAttorneys.and.returnValue(of([]));
    mockNotesService.getNotesByOpportunity.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      declarations: [UWDataGatheringComponent],
      imports: [ReactiveFormsModule, MatDialogModule, HttpClientTestingModule],
      providers: [
        { provide: LawfirmService, useValue: mockLawfirmService },
        { provide: AttorneyService, useValue: mockAttorneyService },
        { provide: NotesService, useValue: mockNotesService },
        { provide: AttachmentService, useValue: mockAttachmentService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(UWDataGatheringComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    component.ngOnInit();
    expect(component.dataGatheringForm).toBeDefined();
    expect(component.dataGatheringForm.get('lawFirm')).toBeDefined();
    expect(component.dataGatheringForm.get('attorney')).toBeDefined();
    expect(
      component.dataGatheringForm.get('attorneyDiscussionNotes'),
    ).toBeDefined();
  });

  it('should load Salesforce data on init', () => {
    component.ngOnInit();
    expect(mockAttorneyService.getFirmSearch).toHaveBeenCalled();
  });

  it('should load attorneys when law firm is selected', () => {
    component.ngOnInit(); // Initialize the form first
    const firmId = 'test-firm-id';
    component.onLawFirmSelected({ value: firmId });

    expect(mockLawfirmService.getLawFirmAttorneys).toHaveBeenCalledWith(firmId);
    expect(component.dataGatheringForm.get('lawFirm')?.value).toBe(firmId);
  });

  it('should populate case notes from Salesforce', () => {
    component.ngOnInit(); // Initialize the form first
    const mockNotes = [
      {
        id: '1',
        title: 'Test Note',
        content: 'Test content',
        lastModifiedDate: new Date(),
        textPreview: 'Test preview',
        createdBy: 'Test User',
        lastModifiedBy: 'Test User',
        owner: 'Test User',
      },
    ];

    component.caseNotes = mockNotes;
    component.populateCaseNotesField();

    const notesValue = component.dataGatheringForm.get(
      'attorneyDiscussionNotes',
    )?.value;
    expect(notesValue).toContain('Test Note');
    expect(notesValue).toContain('Test content');
  });

  it('should mark documents as completed when selections are made', () => {
    component.ngOnInit();

    // Select law firm
    component.onLawFirmSelected({ value: 'test-firm' });
    const lawFirmDoc = component.legalDocuments.find(
      (doc) => doc.id === 'law_firm',
    );
    expect(lawFirmDoc?.completed).toBe(true);

    // Select attorney
    component.onAttorneySelected({ value: 'test-attorney' });
    const attorneyDoc = component.legalDocuments.find(
      (doc) => doc.id === 'attorney',
    );
    expect(attorneyDoc?.completed).toBe(true);
  });

  it('should calculate completion percentage correctly', () => {
    component.ngOnInit();

    // Initially should be 0%
    expect(component.getCompletionPercentage()).toBe(0);

    // Mark some documents as completed
    component.legalDocuments[0].completed = true;
    component.legalDocuments[1].completed = true;

    const percentage = component.getCompletionPercentage();
    expect(percentage).toBeGreaterThan(0);
  });
});


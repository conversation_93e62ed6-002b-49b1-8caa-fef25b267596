.uw-evaluation-container {
  height: 100%;
  padding: 20px;
}

.card-header {
  background-color: var(--color-primary);
  color: white;
  padding: 15px 20px;
  border-radius: 8px 8px 0 0;

  h4 {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin: 0;

    mat-icon {
      margin-right: 10px;
      font-size: 20px;
    }
  }
}

.placeholder-content {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.coming-soon {
  text-align: center;
  max-width: 800px;

  > mat-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 20px;
  }

  h3 {
    color: var(--color-primary);
    margin-bottom: 15px;
    font-weight: 600;
  }

  > p {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
  }
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .feature-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    mat-card-content {
      text-align: center;
      padding: 20px;

      mat-icon {
        font-size: 32px;
        margin-bottom: 10px;
      }

      h5 {
        color: var(--color-primary);
        margin-bottom: 10px;
        font-weight: 600;
      }

      p {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

.development-status {
  mat-chip-list {
    justify-content: center;

    mat-chip {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      font-size: 14px;

      mat-icon {
        margin-right: 6px;
        font-size: 16px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .uw-evaluation-container {
    padding: 10px;
  }

  .placeholder-content {
    padding: 20px;
    min-height: 400px;
  }

  .coming-soon {
    > mat-icon {
      font-size: 48px;
    }

    h3 {
      font-size: 24px;
    }
  }

  .feature-list {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

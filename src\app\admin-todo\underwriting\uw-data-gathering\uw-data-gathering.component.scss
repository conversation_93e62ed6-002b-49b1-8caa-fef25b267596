// Match the styling patterns from other admin components
.uw-data-gathering-container {
  max-height: 75vh;
  overflow-y: auto;
}

.header-section {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--color-content-bg);
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 0;

  h4 {
    color: var(--color-primary);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 18px;
  }

  .book-selection {
    min-width: 300px;
  }

  .progress-section {
    text-align: right;

    h6 {
      margin-bottom: 10px;
      font-weight: 500;
    }

    mat-progress-bar {
      height: 8px;
      border-radius: 4px;
      margin-bottom: 8px;
    }

    .progress-text {
      font-size: 14px;
      color: #666;
    }
  }

  .save-status {
    margin-top: 15px;
    text-align: right;
    font-size: 12px;

    .saving-icon {
      animation: spin 2s linear infinite;
      color: var(--color-accent);
      font-size: 16px;
    }

    .last-saved {
      color: #4caf50;
    }

    .unsaved-changes {
      color: #ff9800;
      font-weight: 500;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.book-info-section {
  margin-bottom: 20px;

  .info-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    mat-card-header {
      background-color: #f8f9fa;
      border-radius: 8px 8px 0 0;

      mat-card-title {
        color: var(--color-primary);
        font-weight: 600;
      }
    }

    .book-type-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      margin-left: 8px;

      &.pca-badge {
        background-color: #e3f2fd;
        color: #1976d2;
      }

      &.medical-badge {
        background-color: #f3e5f5;
        color: #7b1fa2;
      }
    }
  }
}

.section-card {
  margin-bottom: 20px;

  mat-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    mat-card-header {
      background-color: #f8f9fa;
      border-radius: 8px 8px 0 0;
      padding: 16px 20px;

      mat-card-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: var(--color-primary);

        mat-icon {
          margin-right: 10px;
          font-size: 20px;
        }

        .required-indicator {
          margin-left: auto;
          font-size: 12px;
          color: #e91e63;
          font-weight: 400;
        }
      }
    }

    mat-card-content {
      padding: 20px;
    }
  }
}

.document-checklist {
  .document-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    mat-checkbox {
      flex: 1;
    }

    .check-icon {
      color: #4caf50;
      margin-left: 10px;
      font-size: 20px;
    }

    .required-asterisk {
      color: #e91e63;
      font-weight: bold;
      margin-left: 4px;
    }
  }
}

.dec-page-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;

  mat-checkbox {
    .required-asterisk {
      color: #e91e63;
      font-weight: bold;
      margin-left: 4px;
      font-size: 12px;
    }
  }
}

.provider-evidence-card {
  border-left: 4px solid #ff9800;

  .provider-evidence-warning {
    display: flex;
    align-items: center;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 15px;

    mat-icon {
      margin-right: 10px;
    }

    span {
      font-weight: 500;
      color: #856404;
    }
  }

  .provider-evidence-btn {
    margin-top: 15px;

    mat-icon {
      margin-right: 8px;
    }
  }
}

.action-section {
  margin-top: 30px;
  margin-bottom: 30px;

  .action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 20px;

    .action-btn {
      min-width: 200px;
      padding: 12px 20px;

      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .status-display {
    mat-chip-list {
      mat-chip {
        display: flex;
        align-items: center;

        mat-icon {
          margin-right: 6px;
          font-size: 16px;
        }
      }
    }
  }
}

.ai-summary-btn {
  width: 100%;
  height: 60px;

  mat-icon {
    margin-right: 8px;
  }
}

.no-book-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  mat-card {
    width: 400px;
    text-align: center;

    .empty-state {
      padding: 40px;

      mat-icon {
        font-size: 48px;
        color: #ccc;
        margin-bottom: 15px;
      }

      h5 {
        color: #666;
        margin-bottom: 10px;
      }

      p {
        color: #999;
        font-size: 14px;
      }
    }
  }
}

// Form field spacing
.row {
  margin-bottom: 15px;

  .col-md-6,
  .col-md-4,
  .col-md-12,
  .col-md-10,
  .col-md-2 {
    padding-right: 15px;

    &:last-child {
      padding-right: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .uw-data-gathering-container {
    padding: 10px;
  }

  .header-section {
    .progress-section {
      text-align: left;
      margin-top: 20px;
    }

    .save-status {
      text-align: left;
    }
  }

  .action-buttons {
    flex-direction: column;

    .action-btn {
      width: 100%;
      margin-bottom: 10px;
    }
  }

  .ai-summary-btn {
    margin-top: 15px;
  }

  .dec-page-section {
    flex-direction: column;
    gap: 10px;
  }
}

// Custom form field styles
mat-form-field {
  width: 100%;

  &.mat-form-field-appearance-fill {
    .mat-form-field-flex {
      border-radius: 6px;
    }
  }
}

// Custom checkbox styles
mat-checkbox {
  .mat-checkbox-frame {
    border-radius: 3px;
  }
}

// Custom progress bar
mat-progress-bar {
  .mat-progress-bar-buffer {
    background-color: #e0e0e0;
  }
}

// Custom chip styles
mat-chip {
  &.mat-warn {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }
}

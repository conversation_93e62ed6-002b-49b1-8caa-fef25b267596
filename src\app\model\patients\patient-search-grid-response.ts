import { ContactInfo } from './index';

export interface PatientSearchGridResponse {
  patientId: string;
  fullName: string;
  firstName: string;
  lastName: string;
  email: string;
  dob: string;
  doi: string;
  accidentState: string;
  state: string;
  injuries: string;
  stage: string;
  caseStatus: string;
  last90DayCaseUpdateDate: string;
  primaryPhone: string;
  medicalFundingAmount: string;
  medicalFunded: string;
  pcaAmount: string;
  attorneyFullName: string;
  paralegalFullName: string;
  lastActivityDate: string;
  lastModifiedDate: string;
  attorney: ContactInfo;
  paralegal: ContactInfo;
  caseType: string;
}

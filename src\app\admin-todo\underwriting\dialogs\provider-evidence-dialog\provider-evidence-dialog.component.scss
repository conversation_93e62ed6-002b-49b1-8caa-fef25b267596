.provider-evidence-dialog {
  .dialog-content {
    max-height: 80vh;
    overflow-y: auto;
    padding: 20px;
    min-width: 800px;
    
    .evidence-form {
      .section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;
        
        h3 {
          color: var(--color-primary);
          margin-bottom: 20px;
          font-weight: 600;
          font-size: 16px;
          border-bottom: 2px solid var(--color-primary);
          padding-bottom: 8px;
        }
        
        .row {
          margin-bottom: 15px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        .full-width {
          width: 100%;
        }
      }
      
      .documentation-checklist {
        display: flex;
        flex-direction: column;
        gap: 15px;
        
        mat-checkbox {
          .mat-checkbox-label {
            font-size: 14px;
            color: #333;
          }
          
          &.mat-checkbox-checked {
            .mat-checkbox-label {
              color: #28a745;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
  
  .dialog-actions {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    
    button {
      margin-left: 10px;
      min-width: 120px;
    }
  }
}

// Form field styling improvements
:host ::ng-deep {
  .mat-form-field-appearance-fill {
    .mat-form-field-flex {
      background-color: white !important;
      border-radius: 6px;
    }
    
    .mat-form-field-label {
      color: #666 !important;
      font-weight: 500;
    }
    
    .mat-input-element {
      color: #333 !important;
      
      &::placeholder {
        color: #999 !important;
      }
    }
    
    textarea.mat-input-element {
      color: #333 !important;
      
      &::placeholder {
        color: #999 !important;
      }
    }
  }
  
  .mat-select-value-text {
    color: #333 !important;
  }
  
  .mat-select-placeholder {
    color: #999 !important;
  }
  
  // Dialog specific styling
  .mat-dialog-container {
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  }
  
  .mat-dialog-title {
    display: flex;
    align-items: center;
    background-color: var(--color-primary);
    color: white;
    margin: 0;
    padding: 20px;
    font-size: 18px;
    font-weight: 600;
    
    mat-icon {
      margin-right: 10px;
      font-size: 24px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .provider-evidence-dialog {
    .dialog-content {
      min-width: 90vw;
      padding: 15px;
      
      .evidence-form {
        .section {
          padding: 15px;
          margin-bottom: 20px;
        }
      }
    }
    
    .dialog-actions {
      flex-direction: column;
      
      button {
        width: 100%;
        margin: 5px 0;
      }
    }
  }
}

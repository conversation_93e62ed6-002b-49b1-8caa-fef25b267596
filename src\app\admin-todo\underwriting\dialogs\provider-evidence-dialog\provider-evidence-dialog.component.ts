import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface ProviderEvidenceData {
  bookId: string;
  bookName: string;
  existingData?: any;
}

@Component({
  selector: 'app-provider-evidence-dialog',
  templateUrl: './provider-evidence-dialog.component.html',
  styleUrls: ['./provider-evidence-dialog.component.scss']
})
export class ProviderEvidenceDialogComponent implements OnInit {
  providerEvidenceForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ProviderEvidenceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ProviderEvidenceData
  ) {
    this.providerEvidenceForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.data.existingData) {
      this.providerEvidenceForm.patchValue(this.data.existingData);
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      // Provider Information
      providerName: ['', Validators.required],
      facilityType: ['', Validators.required],
      providerAddress: ['', Validators.required],
      
      // Treatment Information
      firstTreatmentDate: [''],
      lastTreatmentDate: [''],
      treatmentSummary: [''],
      
      // Billing Information
      totalBilledAmount: [0, [Validators.min(0)]],
      amountPaid: [0, [Validators.min(0)]],
      billingNotes: [''],
      
      // Documentation Checklist
      hasProviderContract: [false],
      hasLienAgreement: [false],
      hasTreatmentRecords: [false],
      hasBillingRecords: [false],
      hasProviderLicense: [false],
      
      // Additional Notes
      additionalNotes: ['']
    });
  }

  onSave(): void {
    if (this.providerEvidenceForm.valid) {
      const formData = {
        ...this.providerEvidenceForm.value,
        bookId: this.data.bookId,
        bookName: this.data.bookName,
        lastUpdated: new Date()
      };
      
      this.dialogRef.close(formData);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  // Helper method to check if all required documentation is complete
  isDocumentationComplete(): boolean {
    const form = this.providerEvidenceForm.value;
    return form.hasProviderContract && 
           form.hasLienAgreement && 
           form.hasTreatmentRecords && 
           form.hasBillingRecords && 
           form.hasProviderLicense;
  }

  // Helper method to get completion percentage
  getDocumentationCompletionPercentage(): number {
    const form = this.providerEvidenceForm.value;
    const completed = [
      form.hasProviderContract,
      form.hasLienAgreement,
      form.hasTreatmentRecords,
      form.hasBillingRecords,
      form.hasProviderLicense
    ].filter(Boolean).length;
    
    return Math.round((completed / 5) * 100);
  }
}

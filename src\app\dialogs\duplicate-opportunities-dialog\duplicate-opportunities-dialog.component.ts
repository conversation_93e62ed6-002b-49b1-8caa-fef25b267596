import { ComponentType } from '@angular/cdk/portal';
import { Component, Inject } from '@angular/core';
import {
  MatLegacyDialogRef as MatDialogRef,
  MAT_LEGACY_DIALOG_DATA as MAT_DIALOG_DATA,
} from '@angular/material/legacy-dialog';
import { MatTableColumn } from 'src/app/model';
import { DuplicateOpportunitiesMatches } from 'src/app/model/opportunity';
import { PatientSearchGridResponse } from 'src/app/model/patients/patient-search-grid-response';
import { DialogOptions } from 'src/app/services/dialogs/DialogOptions';
import { DialogHelperService } from 'src/app/services/dialogs/dialog-helper.service';
import { PayoffService } from 'src/app/services/admin/payoff.service';
import { AppSnackbarService } from 'src/app/services/helper/app-snackbar.service';
import { cloneDeep } from 'lodash';

export interface DuplicateOpportunitiesDialogData {
  duplicateMatches: DuplicateOpportunitiesMatches;
  currentOpportunityId: string;
  payoffLogID: number;
}

export class DuplicateOpportunitiesDialogOptions implements DialogOptions {
  readonly component: ComponentType<any> =
    DuplicateOpportunitiesDialogComponent;
  panelClass?: string | string[] = 'dialog-container';
  maxWidth?: any = '1000px';
  context?: any;

  constructor(data: DuplicateOpportunitiesDialogData) {
    this.context = data;
  }
}

@Component({
  selector: 'app-duplicate-opportunities-dialog',
  templateUrl: './duplicate-opportunities-dialog.component.html',
  styleUrls: ['./duplicate-opportunities-dialog.component.scss'],
})
export class DuplicateOpportunitiesDialogComponent {
  columns: MatTableColumn[] = [
    { field: 'fullName', name: 'Name' },
    { field: 'dob', name: 'Date of Birth' },
    { field: 'doi', name: 'Date of Injury' },
    { field: 'stage', name: 'Stage' },
    { field: 'caseStatus', name: 'Case Status' },
    { field: 'attorney', name: 'Attorney' },
    { field: 'paralegal', name: 'Paralegal' },
  ];

  possibleColumns = cloneDeep(this.columns);

  confidentMatches: PatientSearchGridResponse[] = [];
  possibleMatches: PatientSearchGridResponse[] = [];
  selectedOpportunities: PatientSearchGridResponse[] = [];
  selectedConfidentMatches: PatientSearchGridResponse[] = [];
  selectedPossibleMatches: PatientSearchGridResponse[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: DuplicateOpportunitiesDialogData,
    private dialogRef: MatDialogRef<DuplicateOpportunitiesDialogComponent>,
    private dialogHelper: DialogHelperService,
    private payoffService: PayoffService,
    private snackbar: AppSnackbarService,
  ) {
    if (this.data.duplicateMatches) {
      const rawConfidentMatches = this.data.duplicateMatches.confidentMatches;
      const rawPossibleMatches = this.data.duplicateMatches.possibleMatches;
      this.confidentMatches = this.transformMatchesData(rawConfidentMatches);
      this.possibleMatches = this.transformMatchesData(rawPossibleMatches);
    }
  }

  private transformMatchesData(matches: any[]): PatientSearchGridResponse[] {
    return matches.map((match) => ({
      ...match,
      attorney: match.attorney?.name || match.attorneyFullName || '',
      paralegal: match.paralegal?.name || match.paralegalFullName || '',
    }));
  }

  openOpportunity(opportunity: PatientSearchGridResponse): void {
    const url = `/admin/todo/patient-detail/${opportunity.patientId}`;
    window.open(url, '_blank');
  }

  async onOpportunitySelected(
    opportunity: PatientSearchGridResponse,
  ): Promise<void> {
    const result = await this.dialogHelper.openWarningDialog(
      'This action will cancel the current reduction request and create a new one for the selected opportunity. Do you want to continue?',
      'Confirm Action',
    );

    if (result) {
      this.processSelectedOpportunities([opportunity]);
    }
  }

  onMultipleOpportunitiesSelected(
    opportunities: PatientSearchGridResponse[],
  ): void {
    this.selectedOpportunities = opportunities;
  }

  onConfidentMatchesSelected(opportunities: PatientSearchGridResponse[]): void {
    this.selectedConfidentMatches = opportunities;
    this.updateTotalSelectedOpportunities();
  }

  onPossibleMatchesSelected(opportunities: PatientSearchGridResponse[]): void {
    this.selectedPossibleMatches = opportunities;
    this.updateTotalSelectedOpportunities();
  }

  private updateTotalSelectedOpportunities(): void {
    this.selectedOpportunities = [
      ...this.selectedConfidentMatches,
      ...this.selectedPossibleMatches,
    ];
  }

  async onSubmitSelectedOpportunities(): Promise<void> {
    if (this.selectedOpportunities.length === 0) {
      this.snackbar.open('Please select at least one opportunity.');
      return;
    }

    const result = await this.dialogHelper.openWarningDialog(
      `This action will cancel the current reduction request and create a new one for ${this.selectedOpportunities.length} selected opportunities. Do you want to continue?`,
      'Confirm Action',
    );

    if (result) {
      this.processSelectedOpportunities(this.selectedOpportunities);
    }
  }

  private processSelectedOpportunities(
    opportunities: PatientSearchGridResponse[],
  ): void {
    const oppIds = opportunities.map((opp) => opp.patientId);

    const request = {
      oppIds: oppIds,
      baseOpportunityId: this.data.currentOpportunityId,
      payoffLogId: this.data.payoffLogID,
    };

    this.payoffService.cancelAndRecreateReduction(request).subscribe(
      (response: boolean | { message: string }) => {
        if (response === true) {
          this.snackbar.open(
            'Successfully canceled current reduction request and created a new one.',
          );
          this.dialogRef.close(true);
        } else {
          this.snackbar.open(
            'Failed to process the request. Please try again.',
          );
        }
      },
      (error) => {
        this.snackbar.open(
          error.message || 'Failed to process the request. Please try again.',
        );
      },
    );
  }
}

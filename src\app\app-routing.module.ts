import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import {
  AccountRequestComponent,
  AuthFormConfirmComponent,
  LoginPageComponent,
  PasswordCreateComponent,
  PasswordForgotComponent,
  PasswordResetComponent,
  VerifyUserComponent,
  TwoFactorAuthComponent,
} from 'src/app/authentication';
import { AichatComponent } from './aichat/aichat.component';
import { AiuploadComponent } from './aiupload/aiupload.component';
import { AuthGuard, UserAdminGuard, GainAdminGuard } from 'src/app/helpers';
import { AccountDetailPageComponent } from './accounts';
import { SessionGuard } from './helpers/session.guard';
import { DashboardComponent } from './home';
import { ProvidersmapComponent } from './providersmap';
import { MyAccountPageComponent } from './my-account';
import {
  PlaintiffUnauthorizedComponent,
  PlantiffsPageComponent,
} from './plaintiffs';
import { ProviderBillingPageComponent } from './provider-billing';
import { PatientDetailPageComponent } from './provider-patient';
import {
  NotificationsPageComponent,
  SupportPageComponent,
  UserAdminDashboardComponent,
} from './user';
import { AdminTodoPageComponent } from './admin-todo';
import { RiskManagerAdminPageComponent } from './admin-todo/risk-manager-admin-page/risk-manager-admin-page.component';
import { ReductionRequestTodoComponent } from './admin-todo/reduction-request-todo/reduction-request-todo.component';
import { PayoffApprovalTodoComponent } from './admin-todo/payoff-approval-todo/payoff-approval-todo.component';
import { DataManualReviewTodoComponent } from './admin-todo/data-manual-review-todo/data-manual-review-todo.component';
import { SearchPlaintiffComponent } from './admin-todo/search-plaintiff/search-plaintiff.component';
import { UnderwritingPageComponent } from './admin-todo/underwriting/underwriting-page/underwriting-page.component';

window.enableAdminFeature = true;

const routes: Routes = [
  { path: 'login', component: LoginPageComponent, canActivate: [SessionGuard] },
  {
    path: 'two-factor',
    component: TwoFactorAuthComponent,
    canActivate: [SessionGuard],
  },
  { path: 'password-create', component: PasswordCreateComponent },
  { path: 'password-forgot', component: PasswordForgotComponent },
  {
    path: 'auth-form-confirm/:type/:name',
    component: AuthFormConfirmComponent,
  },
  { path: 'account-request', component: AccountRequestComponent },
  { path: 'account-request/:email', component: AccountRequestComponent },
  {
    path: 'account-request/:email/:sharer',
    component: AccountRequestComponent,
  },
  { path: 'password-reset/:email/:token', component: PasswordResetComponent },
  { path: 'verify-user/:email/:token', component: VerifyUserComponent },
  {
    path: 'create-account/:email/:firstname/:lastname/:usertype/:phone/:token',
    component: AccountRequestComponent,
  },
  { path: 'create-account/:token', component: AccountRequestComponent },
  { path: 'create-account/:token/:sharer', component: AccountRequestComponent },

  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'user-admin-dashboard',
    component: UserAdminDashboardComponent,
    canActivate: [AuthGuard, UserAdminGuard],
  },

  {
    path: 'providersmap',
    component: ProvidersmapComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'providersmap/search/:sharer/:aid/:c-name/:p-name/:p-specialty/:address',
    component: ProvidersmapComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'patients',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'patients/:status',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'patients/patient-unauthorized',
    component: PlaintiffUnauthorizedComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'clients',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'clients/:status',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'clients/client-unauthorized',
    component: PlaintiffUnauthorizedComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'clients/notification',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'plaintiffs',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'plaintiffs/:plaintiff-unauthorized',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },
  {
    path: 'plaintiffs/plaintiff-unauthorized',
    component: PlaintiffUnauthorizedComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-search/:sharer/:aid/:p-name/:case-status/:attorney/:paralegal/:firm',
    component: PlantiffsPageComponent,
    canActivate: [AuthGuard],
    runGuardsAndResolvers: 'always',
  },

  {
    path: 'provider-billing',
    component: ProviderBillingPageComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'patients/patient-detail/:pid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'patients/patient-detail/:pid/:aid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'clients/client-detail/:pid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'clients/client-detail/:pid/:aid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-detail/:pid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-detail/:pid/:aid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-detail/:pid/:aid/:sharer',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-detail/:pid/:aid/:sharer/:fsection/:fid',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'plaintiffs/plaintiff-detail-download-files/:pid/:aid/:sharer',
    component: PatientDetailPageComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'my-account',
    component: MyAccountPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'support',
    component: SupportPageComponent,
    canActivate: [AuthGuard],
  },
  {
    path: 'user-admin-dashboard',
    component: UserAdminDashboardComponent,
    canActivate: [AuthGuard, UserAdminGuard],
  },

  {
    path: 'accounts/:accid',
    component: AccountDetailPageComponent,
    canActivate: [GainAdminGuard],
  },

  {
    path: 'notifications',
    component: NotificationsPageComponent,
    canActivate: [AuthGuard],
  },

  {
    path: 'admin/todo',
    component: AdminTodoPageComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: window.enableAdminFeature
          ? 'search-plaintiff'
          : 'reduction-request',
        pathMatch: 'full',
      },
      {
        path: 'search-plaintiff',
        component: SearchPlaintiffComponent,
      },
      {
        path: 'reduction-request',
        component: ReductionRequestTodoComponent,
      },
      {
        path: 'payoff-approval',
        component: PayoffApprovalTodoComponent,
      },
      {
        path: 'data-manual-review',
        component: DataManualReviewTodoComponent,
      },
      {
        path: 'underwriting',
        component: UnderwritingPageComponent,
      },
      {
        path: 'patient-detail/:id',
        component: RiskManagerAdminPageComponent,
        canActivate: [GainAdminGuard],
      },
    ],
  },
  {
    path: 'admin/patient-detail/:id',
    component: RiskManagerAdminPageComponent,
    canActivate: [AuthGuard, GainAdminGuard],
  },
  { path: 'aichat', component: AichatComponent, canActivate: [GainAdminGuard] },
  {
    path: 'aiupload',
    component: AiuploadComponent,
    canActivate: [GainAdminGuard],
  },
  { path: '**', redirectTo: 'dashboard' },
];

// const routerOptions: ExtraOptions = {
//   scrollPositionRestoration: 'enabled',
//   anchorScrolling: 'enabled',
//   scrollOffset: [0, 64],
// };

@NgModule({
  imports: [RouterModule.forRoot(routes, { onSameUrlNavigation: 'reload' })],
  exports: [RouterModule],
})
export class AppRoutingModule {}

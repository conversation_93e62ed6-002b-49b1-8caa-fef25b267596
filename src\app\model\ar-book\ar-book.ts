/**
 * AR_Book__c (Accounts Receivable Book) interface
 * Represents a collection/batch of fundings grouped for underwriting
 */
export interface ARBook {
  id: string;
  name: string;
  month: number;
  year: number;
  arType: string; // Partial Advance, Serviced, Purchased, etc.
  accountId: string;
  accountName: string;
  totalAmount: number;
  fundingCount: number;
  createdDate: Date;
  lastModifiedDate: Date;
  status: string;
}

/**
 * Funding__c interface for fundings within an AR Book
 */
export interface ARBookFunding {
  id: string;
  name: string;
  arBookId: string;
  opportunityId: string;
  opportunityName: string;
  amount: number;
  type: string;
  status: string;
  createdDate: Date;
  lastModifiedDate: Date;
  // Related opportunity/plaintiff data
  plaintiffName: string;
  lawFirm: string;
  attorney: string;
  dateOfInjury: Date;
}

/**
 * Filters for searching AR Books
 */
export interface ARBookFilters {
  month?: number;
  year?: number;
  arType?: string;
  accountId?: string;
  status?: string;
  pageSize?: number;
}

/**
 * Response for AR Book search
 */
export interface ARBookSearchResponse {
  books: ARBook[];
  totalCount: number;
}

/**
 * Response for AR Book fundings
 */
export interface ARBookFundingsResponse {
  fundings: ARBookFunding[];
  totalCount: number;
  bookSummary: ARBook;
}

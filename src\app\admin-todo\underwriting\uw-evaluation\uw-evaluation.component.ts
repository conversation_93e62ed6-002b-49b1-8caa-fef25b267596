import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';

@Component({
  selector: 'app-uw-evaluation',
  templateUrl: './uw-evaluation.component.html',
  styleUrls: ['./uw-evaluation.component.scss'],
})
export class UWEvaluationComponent implements OnInit {
  evaluationForm: UntypedFormGroup;

  constructor(private fb: UntypedFormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm(): void {
    this.evaluationForm = this.fb.group({
      selectedBookId: [''],
      aiRiskAssessment: [''],
      evaluationNotes: [''],
      recommendedAction: [''],
      riskScore: [0],
      approvalStatus: ['pending'],
    });
  }

  generateAIEvaluation(): void {
    // Placeholder for AI evaluation functionality
    console.log('AI Evaluation will be implemented here');
  }

  approveBook(): void {
    // Placeholder for approval functionality
    console.log('Book approval will be implemented here');
  }

  rejectBook(): void {
    // Placeholder for rejection functionality
    console.log('Book rejection will be implemented here');
  }
}

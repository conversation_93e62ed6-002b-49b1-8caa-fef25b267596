import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/services/user';
import { Router } from '@angular/router';
import { UserActivityService } from 'src/app/services/helper/user-activity.service';

@Component({
  selector: 'app-underwriting-page',
  templateUrl: './underwriting-page.component.html',
  styleUrls: ['./underwriting-page.component.scss'],
})
export class UnderwritingPageComponent implements OnInit {
  selectedTabIndex = 0;

  constructor(
    private userService: UserService,
    public router: Router,
    private userActivityService: UserActivityService,
  ) {}

  ngOnInit(): void {
    this.userService.getActiveUserPermissions();

    // Add underwriting activity to recent activity table
    this.userActivityService.addActivity('Underwriting');
  }

  onTabChanged(event: any): void {
    this.selectedTabIndex = event.index;
  }
}

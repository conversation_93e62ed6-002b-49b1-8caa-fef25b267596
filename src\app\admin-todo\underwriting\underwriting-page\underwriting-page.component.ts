import { Component, OnInit } from '@angular/core';
import { UserService } from 'src/app/services/user';
import { Router } from '@angular/router';

@Component({
  selector: 'app-underwriting-page',
  templateUrl: './underwriting-page.component.html',
  styleUrls: ['./underwriting-page.component.scss'],
})
export class UnderwritingPageComponent implements OnInit {
  selectedTabIndex = 0;

  constructor(
    private userService: UserService,
    public router: Router,
  ) {}

  ngOnInit(): void {
    this.userService.getActiveUserPermissions();
  }

  onTabChanged(event: any): void {
    this.selectedTabIndex = event.index;
  }
}

import { ScrollingModule } from '@angular/cdk/scrolling';
import {
  <PERSON><PERSON><PERSON>cyPipe,
  PercentPipe,
  CommonModule,
  DatePipe,
} from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AichatComponent } from './aichat/aichat.component';
import {
  CustomValidators,
  ErrorInterceptor,
  JwtInterceptor,
  SpinnerInterceptor,
} from './helpers';
import { MaterialModule } from './material.module';
import { CollapsibleSidebarComponent } from './shared/collapsible-sidebar/collapsible-sidebar.component';

import {
  AccountRequestComponent,
  AuthFormConfirmComponent,
  LoginPageComponent,
  PasswordCreateComponent,
  PasswordForgotComponent,
  PasswordResetComponent,
  VerifyUserComponent,
  TwoFactorAuthComponent,
} from './authentication';

import { HeaderComponent, SideNavComponent } from './navigation';

import {
  AccountContactsComponent,
  AdminDashboardComponent,
  DashboardComponent,
  OptionsRowComponent,
} from './home';

import {
  PlaintiffUnauthorizedComponent,
  PlantiffsPageComponent,
  PlaintiffsFiltersComponent,
} from './plaintiffs';

import {
  AddAttorneyContactDialogComponent,
  AddChildAccountDialogComponent,
  AddDomainDialogComponent,
  AddFirmDialogComponent,
  AdminAddAccountDialogComponent,
  AdminReductionNegotiationDialogComponent,
  AdminPayoffApprovalDialogComponent,
  BillingSummaryDialogComponent,
  CaseStatusDialogComponent,
  CaseTypeDialogComponent,
  CashAdvanceDialogComponent,
  ChangeOrganizationAccountDialogComponent,
  DataIntegrationReviewComponent,
  DownloadFilesBytypeDialogComponent,
  DupeDifferentProviderDialogComponent,
  DuplicateOpportunitiesDialogComponent,
  FileUploadDialogComponent,
  GetPayoffDialogComponent,
  IdleDialogComponent,
  InputClientaddressDialogComponent,
  InviteUserDialogComponent,
  LoginNotificationDialogComponent,
  MedicalFundingDialogComponent,
  PaymentInfoComponent,
  ProviderIntakeDialogComponent,
  ReductionRequestAddAttorneyComponent,
  ReductionBreakdownDialogComponent,
  RequestDocumentsDialogComponent,
  RequestPharmacyCardComponent,
  RequestTransportationDialogComponent,
  RollupAccountTreeDialogComponent,
  SelectProviderDialogComponent,
  WarningDialogComponent,
  GridDialogComponent,
  BaseDialogComponent,
  CaseDetailDialogComponent,
  ReductionAmountUpdateDialogComponent,
  SelectItemDialogComponent,
  AccidentStateDialogComponent,
  NoteDetailDialogComponent,
} from './dialogs';

import { PdfDialogComponent } from './dialogs/pdf-dialog/pdf-dialog.component';
import { ShareableLinkDialogComponent } from './dialogs/shareable-link-dialog/shareable-link-dialog.component';

import { SpinnerOverlayComponent } from './loading';

import {
  AccountFacilityFieldComponent,
  AccountSearchFieldComponent,
  Address1FieldComponent,
  AttorneyFieldComponent,
  AttorneySearchFieldComponent,
  CaseTypeFieldComponent,
  CheckboxFieldComponent,
  EmailChipListComponent,
  EmailFieldComponent,
  FirmSearchFieldComponent,
  InsuranceLimitsFieldComponent,
  InsurancePolicyTypeFieldComponent,
  MatTableTemplateComponent,
  NoAutofillDirective,
  OrgAccountSearchComponent,
  PatientSelectComponent,
  PhoneFieldComponent,
  PortalAccountFieldComponent,
  PortalUserFieldComponent,
  ReductionRequestConfigComponent,
  ReductionNotesFieldComponent,
  ShareableLinkIconComponent,
  StatesFieldComponent,
  ZipFieldComponent,
  YesNoFieldComponent,
} from './field-components';

import {
  AppointmentDetailsFormComponent,
  InjuryDetailsFormComponent,
  ProviderIntakeFormComponent,
} from './provider-intake';

import {
  DateFormatPipe,
  HumanizePipe,
  PercentagePipe,
  PhonePipe,
} from './helpers';

import {
  ProviderBillingClaimsComponent,
  ProviderBillingDictationsComponent,
  ProviderBillingPageComponent,
} from './provider-billing';

import { ProvidersmapComponent } from './providersmap';

import {
  AddressInfoComponent,
  RecipientInfoComponent,
} from './form-components';

import {
  ClientFundingComponent,
  PatientAttachmentInfoComponent,
  PatientAttorneyInfoComponent,
  PatientCaseInfoComponent,
  PatientClaimInfoComponent,
  PatientDetailPageComponent,
  PatientInfoComponent,
  PatientNotesComponent,
  CaseStatusInfoComponent,
} from './provider-patient';

import {
  NotificationsPageComponent,
  SupportPageComponent,
  UserAdminDashboardComponent,
  UserIconComponent,
} from './user';

import { MyAccountPageComponent } from './my-account';

import {
  AttorneyCashClientComponent,
  AttorneyClientInfoComponent,
  AttorneyIntakeFormComponent,
  AttorneyMedicalClientComponent,
  IntakeAccountInfoComponent,
} from './attorney-intake';

import { MatTreeModule } from '@angular/material/tree';
import {
  AccountDetailPageComponent,
  AccountInfoComponent,
  AccountUserInfoComponent,
  ChildAccountsComponent,
  DomainInfoComponent,
} from './accounts';
import { ParsedPatientDialogComponent } from './dialogs/parsed-patient-dialog/parsed-patient-dialog.component';
import { ParsedSnackbarComponent } from './dialogs/parsed-snackbar/parsed-snackbar.component';
import { AutocompletePositionDirective } from './directives/auto-complete-position.directive';
import { RecipientInitialInfoComponent } from './form-components/recipient-initial-info/recipient-initial-info.component';

import {
  PcaPromoInfoComponent,
  TableHeaderComponent,
  LastActivityComponent,
} from './info-components';

import {
  AdminTodoPageComponent,
  DataManualReviewTodoComponent,
  ReductionRequestTodoComponent,
  PayoffApprovalTodoComponent,
  CaseTableComponent,
  NotesTableComponent,
  UnderwritingPageComponent,
  UWQueueComponent,
  UWDataGatheringComponent,
  UWEvaluationComponent,
  ProviderEvidenceDialogComponent,
} from './admin-todo';
import { FormComponentsModule } from './form-components/form-components.module';
import { FieldComponentsModule } from './field-components/field-components.module';
import { SelectFieldComponent } from './field-components/select-field/select-field.component';
import { SearchFieldComponent } from './field-components/search-field/search-field.component';
import { PatientsSearchFieldComponent } from './field-components/patients-search-field/patients-search-field.component';
import { AdvancedFiltersDialogComponent } from './dialogs/advanced-filters-dialog/advanced-filters-dialog.component';
import { RiskManagerAdminPageComponent } from './admin-todo/risk-manager-admin-page/risk-manager-admin-page.component';
import { CardComponent } from './form-components/card/card.component';
import { TableRowComponent } from './form-components/table-row/table-row.component';
import { SearchPlaintiffComponent } from './admin-todo/search-plaintiff/search-plaintiff.component';
import { CaseStatusFieldComponent } from './field-components/case-status-field/case-status-field.component';
import { CommentsTablistComponent } from './admin-todo/components/comments-tablist/comments-tablist.component';
import { AiuploadComponent } from './aiupload/aiupload.component';
import { EditableSectionComponent } from './form-components/editable-section/editable-section.component';
import { OpportunityCommentsComponent } from './provider-patient/opportunity-comments/opportunity-comments.component';
import { AlertComponent } from './shared/alert/alert.component';
import { MatTableTokenAliasDirective } from './field-components/mat-table-template/mat-table-token-alias.directive';

@NgModule({
  declarations: [
    AppComponent,
    LoginPageComponent,
    TwoFactorAuthComponent,
    PasswordCreateComponent,
    PasswordForgotComponent,
    AuthFormConfirmComponent,
    AccountRequestComponent,
    HeaderComponent,
    SideNavComponent,
    DashboardComponent,
    OptionsRowComponent,
    AccountContactsComponent,
    PlantiffsPageComponent,
    PlaintiffsFiltersComponent,
    IdleDialogComponent,
    SpinnerOverlayComponent,
    PhoneFieldComponent,
    PasswordResetComponent,
    GetPayoffDialogComponent,
    CashAdvanceDialogComponent,
    MedicalFundingDialogComponent,
    MatTableTemplateComponent,
    HumanizePipe,
    DateFormatPipe,
    PercentagePipe,
    ProviderBillingPageComponent,
    ProviderIntakeFormComponent,
    ProviderIntakeDialogComponent,
    RecipientInfoComponent,
    AddressInfoComponent,
    EmailFieldComponent,
    InjuryDetailsFormComponent,
    AppointmentDetailsFormComponent,
    PatientSelectComponent,
    PatientDetailPageComponent,
    PatientInfoComponent,
    PatientAttorneyInfoComponent,
    PatientCaseInfoComponent,
    UserIconComponent,
    PatientAttachmentInfoComponent,
    PdfDialogComponent,
    FileUploadDialogComponent,
    ProviderBillingClaimsComponent,
    ProviderBillingDictationsComponent,
    AttorneyFieldComponent,
    WarningDialogComponent,
    BillingSummaryDialogComponent,
    ReductionRequestAddAttorneyComponent,
    PhonePipe,
    MyAccountPageComponent,
    StatesFieldComponent,
    PatientClaimInfoComponent,
    AttorneyIntakeFormComponent,
    AttorneyMedicalClientComponent,
    AttorneyCashClientComponent,
    AttorneyClientInfoComponent,
    IntakeAccountInfoComponent,
    CaseTypeFieldComponent,
    CaseTypeDialogComponent,
    AddAttorneyContactDialogComponent,
    ReductionAmountUpdateDialogComponent,
    ClientFundingComponent,
    AccountSearchFieldComponent,
    SupportPageComponent,
    UserAdminDashboardComponent,
    VerifyUserComponent,
    InviteUserDialogComponent,
    AdminDashboardComponent,
    AccountDetailPageComponent,
    AccountInfoComponent,
    AccountUserInfoComponent,
    ParsedPatientDialogComponent,
    RecipientInitialInfoComponent,
    AdminAddAccountDialogComponent,
    AttorneySearchFieldComponent,
    InsuranceLimitsFieldComponent,
    RequestDocumentsDialogComponent,
    DomainInfoComponent,
    CaseStatusDialogComponent,
    CaseTypeDialogComponent,
    NotificationsPageComponent,
    AddDomainDialogComponent,
    PatientNotesComponent,
    ParsedSnackbarComponent,
    DupeDifferentProviderDialogComponent,
    ProvidersmapComponent,
    SelectProviderDialogComponent,
    ZipFieldComponent,
    Address1FieldComponent,
    InputClientaddressDialogComponent,
    ChangeOrganizationAccountDialogComponent,
    OrgAccountSearchComponent,
    ChildAccountsComponent,
    BaseDialogComponent,
    CaseDetailDialogComponent,
    NoteDetailDialogComponent,
    AddChildAccountDialogComponent,
    AutocompletePositionDirective,
    FirmSearchFieldComponent,
    AddFirmDialogComponent,
    PlaintiffUnauthorizedComponent,
    DownloadFilesBytypeDialogComponent,
    ShareableLinkDialogComponent,
    ShareableLinkIconComponent,
    EmailChipListComponent,
    RollupAccountTreeDialogComponent,
    AccountFacilityFieldComponent,
    AdminTodoPageComponent,
    ReductionRequestTodoComponent,
    PayoffApprovalTodoComponent,
    DataManualReviewTodoComponent,
    UnderwritingPageComponent,
    UWQueueComponent,
    UWDataGatheringComponent,
    UWEvaluationComponent,
    ProviderEvidenceDialogComponent,
    DataIntegrationReviewComponent,
    AdminReductionNegotiationDialogComponent,
    PortalUserFieldComponent,
    PortalAccountFieldComponent,
    LoginNotificationDialogComponent,
    PcaPromoInfoComponent,
    LastActivityComponent,
    RequestTransportationDialogComponent,
    RequestPharmacyCardComponent,
    InsurancePolicyTypeFieldComponent,
    ReductionRequestConfigComponent,
    PaymentInfoComponent,
    AdminPayoffApprovalDialogComponent,
    GridDialogComponent,
    CheckboxFieldComponent,
    NoAutofillDirective,
    SelectItemDialogComponent,
    AccidentStateDialogComponent,
    ReductionNotesFieldComponent,
    ReductionBreakdownDialogComponent,
    TableHeaderComponent,
    YesNoFieldComponent,
    SelectFieldComponent,
    SearchFieldComponent,
    PatientsSearchFieldComponent,
    AdvancedFiltersDialogComponent,
    RiskManagerAdminPageComponent,
    TableRowComponent,
    SearchPlaintiffComponent,
    CaseStatusFieldComponent,
    CaseTableComponent,
    NotesTableComponent,
    CollapsibleSidebarComponent,
    AichatComponent,
    AiuploadComponent,
    MatTableTokenAliasDirective,
    OpportunityCommentsComponent,
    CommentsTablistComponent,
    DuplicateOpportunitiesDialogComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    FormsModule,
    MaterialModule,
    HttpClientModule,
    NgIdleKeepaliveModule.forRoot(),
    ReactiveFormsModule,
    MatMomentDateModule,
    PdfViewerModule,
    ScrollingModule,
    MatTreeModule,
    CommonModule,
    FieldComponentsModule,
    FormComponentsModule,
    EditableSectionComponent,
    CardComponent,
    CaseStatusInfoComponent,
    AlertComponent,
  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: SpinnerInterceptor, multi: true },
    CurrencyPipe,
    PercentPipe,
    CustomValidators,
    DatePipe,
    HumanizePipe,
    DateFormatPipe,
    PercentagePipe,
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

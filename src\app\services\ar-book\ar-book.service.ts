import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import config from '../../../app.config.json';
import {
  ARBook,
  ARBookFilters,
  ARBookSearchResponse,
  ARBookFundingsResponse,
} from 'src/app/model/ar-book/ar-book';

@Injectable({
  providedIn: 'root',
})
export class ARBookService {
  private apiUrl = config.apiUrl;

  constructor(private http: HttpClient) {}

  /**
   * Search for AR Books based on filters
   * @param filters Search criteria for AR Books
   * @returns Observable of AR Book search results
   */
  searchARBooks(filters: ARBookFilters = {}): Observable<ARBookSearchResponse> {
    let params = new HttpParams();

    if (filters.month) {
      params = params.set('month', filters.month.toString());
    }
    if (filters.year) {
      params = params.set('year', filters.year.toString());
    }
    if (filters.arType) {
      params = params.set('arType', filters.arType);
    }
    if (filters.accountId) {
      params = params.set('accountId', filters.accountId);
    }
    if (filters.status) {
      params = params.set('status', filters.status);
    }
    if (filters.pageSize) {
      params = params.set('pageSize', filters.pageSize.toString());
    }

    return this.http.get<ARBookSearchResponse>(`${this.apiUrl}/api/ar-books`, {
      params,
    });
  }

  /**
   * Get all fundings for a specific AR Book
   * @param arBookId The AR Book ID
   * @returns Observable of fundings within the book
   */
  getARBookFundings(arBookId: string): Observable<ARBookFundingsResponse> {
    return this.http.get<ARBookFundingsResponse>(
      `${this.apiUrl}/api/ar-books/${arBookId}/fundings`,
    );
  }

  /**
   * Get AR Book details by ID
   * @param arBookId The AR Book ID
   * @returns Observable of AR Book details
   */
  getARBookDetails(arBookId: string): Observable<ARBook> {
    return this.http.get<ARBook>(`${this.apiUrl}/api/ar-books/${arBookId}`);
  }

  /**
   * Get recent AR Books for quick selection
   * @param limit Number of recent books to return
   * @returns Observable of recent AR Books
   */
  getRecentARBooks(limit = 10): Observable<ARBook[]> {
    const params = new HttpParams().set('limit', limit.toString());

    return this.http.get<ARBook[]>(`${this.apiUrl}/api/ar-books/recent`, {
      params,
    });
  }

  /**
   * Get AR Book types for filtering
   * @returns Observable of available AR types
   */
  getARBookTypes(): Observable<string[]> {
    return this.http.get<string[]>(`${this.apiUrl}/api/ar-books/types`);
  }
}


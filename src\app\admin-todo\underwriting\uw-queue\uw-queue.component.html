<div class="container-fluid uw-queue-container">
  <form [formGroup]="filterForm" class="row pb-3 filter-row">
    <div class="col-2">
      <app-select-field
        [form]="filterForm"
        formControlName="uwName"
        [items]="underwriters"
        placeholder="UW Name"
        [showClearButton]="true"
        appearance="legacy"
        descProperty="name"
        idProperty="id"
      ></app-select-field>
    </div>

    <div class="col-2">
      <app-select-field
        [form]="filterForm"
        formControlName="bookName"
        [items]="openBooks"
        placeholder="Book Name"
        [showClearButton]="true"
        appearance="legacy"
      ></app-select-field>
    </div>

    <div class="col-2">
      <mat-button-toggle-group
        [value]="filterForm.get('type')?.value"
        (change)="onBookTypeToggle($event.value)"
        appearance="standard"
      >
        <mat-button-toggle value="PCA">PCA</mat-button-toggle>
        <mat-button-toggle value="Medical">Medical</mat-button-toggle>
      </mat-button-toggle-group>
      <label class="toggle-label">Type</label>
    </div>

    <div class="col-2">
      <mat-button-toggle-group
        [value]="filterForm.get('status')?.value"
        (change)="onStatusToggle($event.value)"
        appearance="standard"
      >
        <mat-button-toggle value="open">Open</mat-button-toggle>
        <mat-button-toggle value="completed">Completed</mat-button-toggle>
      </mat-button-toggle-group>
      <label class="toggle-label">Status</label>
    </div>

    <div class="col-2">
      <app-input-field
        [form]="filterForm"
        formControlName="name"
        placeholder="Plaintiff Name"
        [showClearButton]="true"
        appearance="legacy"
      ></app-input-field>
    </div>

    <div class="col-2">
      <button
        mat-raised-button
        color="warn"
        (click)="clearFilters()"
        class="clear-filters-btn"
      >
        Clear All Filters
      </button>
    </div>
  </form>

  <!-- Results Table -->
  <div class="results-section">
    <mat-table [dataSource]="dataSource" class="books-table" matSort>
      <!-- Expand Column -->
      <ng-container matColumnDef="expand">
        <mat-header-cell *matHeaderCellDef></mat-header-cell>
        <mat-cell *matCellDef="let book">
          <button
            mat-icon-button
            (click)="toggleBookExpansion(book)"
            [attr.aria-label]="book.expanded ? 'Collapse' : 'Expand'"
          >
            <mat-icon>{{
              book.expanded ? "expand_less" : "expand_more"
            }}</mat-icon>
          </button>
        </mat-cell>
      </ng-container>

      <!-- Book Name Column -->
      <ng-container matColumnDef="bookName">
        <mat-header-cell *matHeaderCellDef mat-sort-header
          >Book Name</mat-header-cell
        >
        <mat-cell *matCellDef="let book" class="book-name-cell">
          <div class="book-name">{{ book.bookName }}</div>
        </mat-cell>
      </ng-container>

      <!-- Create Date Column -->
      <ng-container matColumnDef="createDate">
        <mat-header-cell *matHeaderCellDef mat-sort-header
          >Create Date</mat-header-cell
        >
        <mat-cell *matCellDef="let book">{{
          book.createDate | date: "short"
        }}</mat-cell>
      </ng-container>

      <!-- Count of Plaintiffs Column -->
      <ng-container matColumnDef="plaintiffCount">
        <mat-header-cell *matHeaderCellDef mat-sort-header
          >Count of Plaintiffs</mat-header-cell
        >
        <mat-cell *matCellDef="let book">{{ book.plaintiffCount }}</mat-cell>
      </ng-container>

      <!-- Book Type Column -->
      <ng-container matColumnDef="bookType">
        <mat-header-cell *matHeaderCellDef mat-sort-header
          >Book Type</mat-header-cell
        >
        <mat-cell *matCellDef="let book">
          <span
            class="book-type-badge"
            [ngClass]="{
              'pca-badge': book.bookType === 'PCA',
              'medical-badge': book.bookType === 'Medical'
            }"
          >
            {{ book.bookType }}
          </span>
        </mat-cell>
      </ng-container>

      <!-- Total Amount Requested Column -->
      <ng-container matColumnDef="totalAmountRequested">
        <mat-header-cell *matHeaderCellDef mat-sort-header
          >Total Amount Requested</mat-header-cell
        >
        <mat-cell *matCellDef="let book">{{
          book.totalAmountRequested | currency
        }}</mat-cell>
      </ng-container>

      <!-- Actions Column -->
      <ng-container matColumnDef="actions">
        <mat-header-cell *matHeaderCellDef>Actions</mat-header-cell>
        <mat-cell *matCellDef="let book">
          <button
            mat-raised-button
            color="primary"
            size="small"
            (click)="openDataGathering(book.id)"
            class="action-btn"
          >
            Data Gathering
          </button>
          <button
            mat-raised-button
            color="accent"
            size="small"
            (click)="openEvaluation(book.id)"
            class="action-btn ml-2"
          >
            Evaluation
          </button>
        </mat-cell>
      </ng-container>

      <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
      <mat-row
        *matRowDef="let book; columns: displayedColumns"
        class="book-row"
      ></mat-row>

      <!-- Expanded Content (Plaintiffs) -->
      <ng-container *ngFor="let book of dataSource.data">
        <mat-row *ngIf="book.expanded" class="expanded-content">
          <mat-cell [attr.colspan]="displayedColumns.length">
            <div class="plaintiffs-section">
              <h5>Plaintiffs in {{ book.bookName }}</h5>

              <table
                mat-table
                [dataSource]="book.plaintiffs"
                class="plaintiffs-table"
              >
                <!-- First Name Column -->
                <ng-container matColumnDef="firstName">
                  <th mat-header-cell *matHeaderCellDef>First Name</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.firstName }}
                  </td>
                </ng-container>

                <!-- Last Name Column -->
                <ng-container matColumnDef="lastName">
                  <th mat-header-cell *matHeaderCellDef>Last Name</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.lastName }}
                  </td>
                </ng-container>

                <!-- DOB Column -->
                <ng-container matColumnDef="dateOfBirth">
                  <th mat-header-cell *matHeaderCellDef>DOB</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.dateOfBirth | date: "shortDate" }}
                  </td>
                </ng-container>

                <!-- DOI Column -->
                <ng-container matColumnDef="dateOfInjury">
                  <th mat-header-cell *matHeaderCellDef>DOI</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.dateOfInjury | date: "shortDate" }}
                  </td>
                </ng-container>

                <!-- Law Firm Column -->
                <ng-container matColumnDef="lawFirm">
                  <th mat-header-cell *matHeaderCellDef>Law Firm</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.lawFirm }}
                  </td>
                </ng-container>

                <!-- Requested Amount Column -->
                <ng-container matColumnDef="requestedAmount">
                  <th mat-header-cell *matHeaderCellDef>Requested Amount</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    {{ plaintiff.requestedAmount | currency }}
                  </td>
                </ng-container>

                <!-- Plaintiff Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef>Actions</th>
                  <td mat-cell *matCellDef="let plaintiff">
                    <button
                      mat-button
                      color="primary"
                      (click)="openPlaintiffDetail(plaintiff.id)"
                    >
                      View Details
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="plaintiffColumns"></tr>
                <tr
                  mat-row
                  *matRowDef="let row; columns: plaintiffColumns"
                  class="plaintiff-row"
                ></tr>
              </table>
            </div>
          </mat-cell>
        </mat-row>
      </ng-container>
    </mat-table>

    <!-- Paginator -->
    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons>
    </mat-paginator>
  </div>
</div>

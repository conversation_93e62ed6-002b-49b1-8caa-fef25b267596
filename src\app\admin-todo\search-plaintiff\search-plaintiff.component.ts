import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { PatientService } from 'src/app/services/provider/patient.service';
import { PatientsSearchAllResponse } from 'src/app/model/patients';
import { MatTableColumn, User } from 'src/app/model';
import { MatTableTemplateComponent } from 'src/app/field-components';
import { AuthenticationService } from 'src/app/services/authentication';
import { OpportunityFilters } from 'src/app/model/filters';
import { CaseStatusService } from 'src/app/provider-patient/services/case-status/case-status.service';
import { UserActivityService } from 'src/app/services/helper/user-activity.service';

@Component({
  selector: 'app-search-plaintiff',
  templateUrl: './search-plaintiff.component.html',
  styleUrls: ['./search-plaintiff.component.scss'],
})
export class SearchPlaintiffComponent implements OnInit {
  @ViewChild(MatTableTemplateComponent) table!: MatTableTemplateComponent;

  pageSize = 250;
  itemLimit = false;
  clickableColumns: string[] = ['status'];
  columns: MatTableColumn[] = [
    { field: 'name', name: 'Name' },
    { field: 'dob', name: 'Date of Birth' },
    { field: 'doa', name: 'Date of Injury' },
    {
      field: 'status',
      name: 'Case Status',
      tooltip: this.caseStatusService.getCaseStatusTooltip(),
      tooltipClass: this.caseStatusService.getCaseStatusTooltipClass(),
      styling: this.statusStyling.bind(this),
    },
    { field: 'attorney', name: 'Attorney' },
    { field: 'paralegal', name: 'Paralegal' },
    { field: 'medicalInvoice', name: 'Medical Invoice', type: 'currency' },
    { field: 'pcaAmount', name: 'PCA Amount', type: 'currency' },
    { field: 'lawFirm', name: 'Law Firm' },
  ];
  user!: User;
  currentFilters?: OpportunityFilters;

  constructor(
    private patientService: PatientService,
    private router: Router,
    private authService: AuthenticationService,
    private caseStatusService: CaseStatusService,
    private userActivityService: UserActivityService,
  ) {}

  ngOnInit(): void {
    this.authService.currentUser.subscribe((user) => {
      this.user = user;
    });

    this.userActivityService.addActivity('Search Plaintiffs');
  }

  onFiltersChanged(filters: OpportunityFilters): void {
    this.currentFilters = filters;
    this.searchAll(filters);
  }

  initializeTable(data: PatientsSearchAllResponse[]) {
    this.table.updateTableData(data);
  }

  searchAll(filters: OpportunityFilters) {
    filters.pageSize = this.pageSize;
    this.patientService.searchAll(filters).subscribe((q) => {
      if (q.length == 0 && !this.table.data) {
        return;
      }

      this.itemLimit = q.length == this.pageSize;

      this.initializeTable(q);
    });
  }

  onPlaintiffSelected(plaintiff: PatientsSearchAllResponse) {
    if (plaintiff && plaintiff.id) {
      console.log(
        '🧪 TESTING: Selected plaintiff opportunity ID:',
        plaintiff.id,
      );
      console.log('🧪 TESTING: Full plaintiff data:', plaintiff);
      this.router.navigate(['/admin/todo/patient-detail', plaintiff.id]);
    }
  }

  private statusStyling(plaintiff: PatientsSearchAllResponse) {
    return this.caseStatusService.getStatusStyling(
      plaintiff.statusDate,
      plaintiff.stage,
    );
  }

  onColumnClick(data: { col: string; row: PatientsSearchAllResponse }) {
    if (data.col === 'status') {
      this.caseStatusService
        .processCaseStatusWithPlaintiffId(data.row.id)
        .subscribe((result: { Status: string; Type: string }) => {
          if (result) {
            this.searchAll(this.currentFilters || { pageSize: this.pageSize });
          }
        });
    }
  }
}

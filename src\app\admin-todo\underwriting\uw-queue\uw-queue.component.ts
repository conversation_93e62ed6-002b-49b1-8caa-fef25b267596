import { Component, OnInit, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { UserService } from 'src/app/services/user';

export interface UWBook {
  id: string;
  bookName: string;
  createDate: Date;
  plaintiffCount: number;
  bookType: 'PCA' | 'Medical';
  totalAmountRequested: number;
  plaintiffs: UWPlaintiff[];
  expanded?: boolean;
}

export interface UWPlaintiff {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  dateOfInjury: Date;
  lawFirm: string;
  requestedAmount: number;
  bookId: string;
}

@Component({
  selector: 'app-uw-queue',
  templateUrl: './uw-queue.component.html',
  styleUrls: ['./uw-queue.component.scss'],
})
export class UWQueueComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;

  filterForm: UntypedFormGroup;
  dataSource = new MatTableDataSource<UWBook>();

  // Filter options
  underwriters: any[] = [];
  openBooks: string[] = [];

  // Table columns
  displayedColumns: string[] = [
    'expand',
    'bookName',
    'createDate',
    'plaintiffCount',
    'bookType',
    'totalAmountRequested',
    'actions',
  ];

  plaintiffColumns: string[] = [
    'firstName',
    'lastName',
    'dateOfBirth',
    'dateOfInjury',
    'lawFirm',
    'requestedAmount',
    'actions',
  ];

  // Sample data - replace with real service calls
  sampleBooks: UWBook[] = [
    {
      id: '1',
      bookName: 'Motor Vehicle Cases - Q1 2024',
      createDate: new Date('2024-01-15'),
      plaintiffCount: 15,
      bookType: 'PCA',
      totalAmountRequested: 450000,
      expanded: false,
      plaintiffs: [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Smith',
          dateOfBirth: new Date('1985-03-15'),
          dateOfInjury: new Date('2023-12-10'),
          lawFirm: 'Smith & Associates',
          requestedAmount: 25000,
          bookId: '1',
        },
        {
          id: '2',
          firstName: 'Jane',
          lastName: 'Doe',
          dateOfBirth: new Date('1990-07-22'),
          dateOfInjury: new Date('2023-11-05'),
          lawFirm: 'Legal Partners LLC',
          requestedAmount: 35000,
          bookId: '1',
        },
      ],
    },
    {
      id: '2',
      bookName: 'Medical Malpractice - December',
      createDate: new Date('2023-12-01'),
      plaintiffCount: 8,
      bookType: 'Medical',
      totalAmountRequested: 320000,
      expanded: false,
      plaintiffs: [
        {
          id: '3',
          firstName: 'Robert',
          lastName: 'Johnson',
          dateOfBirth: new Date('1975-11-30'),
          dateOfInjury: new Date('2023-10-15'),
          lawFirm: 'Medical Law Group',
          requestedAmount: 50000,
          bookId: '2',
        },
        {
          id: '4',
          firstName: 'Maria',
          lastName: 'Garcia',
          dateOfBirth: new Date('1988-03-22'),
          dateOfInjury: new Date('2023-11-01'),
          lawFirm: 'Garcia & Associates',
          requestedAmount: 42000,
          bookId: '2',
        },
        {
          id: '5',
          firstName: 'James',
          lastName: 'Wilson',
          dateOfBirth: new Date('1982-08-14'),
          dateOfInjury: new Date('2023-10-25'),
          lawFirm: 'Wilson Legal Group',
          requestedAmount: 38000,
          bookId: '2',
        },
      ],
    },
    {
      id: '3',
      bookName: 'Product Liability - January',
      createDate: new Date('2024-03-10'),
      plaintiffCount: 12,
      bookType: 'PCA',
      totalAmountRequested: 680000,
      expanded: false,
      plaintiffs: [
        {
          id: '6',
          firstName: 'Jennifer',
          lastName: 'Anderson',
          dateOfBirth: new Date('1986-05-22'),
          dateOfInjury: new Date('2024-01-10'),
          lawFirm: 'Anderson Law Firm',
          requestedAmount: 65000,
          bookId: '3',
        },
        {
          id: '7',
          firstName: 'Thomas',
          lastName: 'Taylor',
          dateOfBirth: new Date('1974-08-17'),
          dateOfInjury: new Date('2024-01-15'),
          lawFirm: 'Taylor & Partners',
          requestedAmount: 75000,
          bookId: '3',
        },
        {
          id: '8',
          firstName: 'Lisa',
          lastName: 'White',
          dateOfBirth: new Date('1988-02-28'),
          dateOfInjury: new Date('2024-01-20'),
          lawFirm: 'White Legal Services',
          requestedAmount: 58000,
          bookId: '3',
        },
      ],
    },
    {
      id: '4',
      bookName: 'Workers Comp - Q4 2023',
      createDate: new Date('2024-03-20'),
      plaintiffCount: 6,
      bookType: 'Medical',
      totalAmountRequested: 280000,
      expanded: false,
      plaintiffs: [
        {
          id: '9',
          firstName: 'Kevin',
          lastName: 'Thompson',
          dateOfBirth: new Date('1981-10-05'),
          dateOfInjury: new Date('2023-11-01'),
          lawFirm: 'Thompson Law Group',
          requestedAmount: 48000,
          bookId: '4',
        },
        {
          id: '10',
          firstName: 'Michelle',
          lastName: 'Clark',
          dateOfBirth: new Date('1989-03-12'),
          dateOfInjury: new Date('2023-11-10'),
          lawFirm: 'Clark & Associates',
          requestedAmount: 52000,
          bookId: '4',
        },
      ],
    },
    {
      id: '5',
      bookName: 'Slip and Fall Cases - February',
      createDate: new Date('2024-04-01'),
      plaintiffCount: 9,
      bookType: 'PCA',
      totalAmountRequested: 385000,
      expanded: false,
      plaintiffs: [
        {
          id: '11',
          firstName: 'Amanda',
          lastName: 'Rodriguez',
          dateOfBirth: new Date('1984-07-08'),
          dateOfInjury: new Date('2024-02-05'),
          lawFirm: 'Rodriguez Law Firm',
          requestedAmount: 42000,
          bookId: '5',
        },
        {
          id: '12',
          firstName: 'Daniel',
          lastName: 'Hall',
          dateOfBirth: new Date('1992-01-15'),
          dateOfInjury: new Date('2024-02-12'),
          lawFirm: 'Hall & Partners',
          requestedAmount: 38000,
          bookId: '5',
        },
      ],
    },
  ];

  constructor(
    private fb: UntypedFormBuilder,
    private userService: UserService,
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
    this.loadUnderwriters();
  }

  ngAfterViewInit(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  initializeForm(): void {
    this.filterForm = this.fb.group({
      uwName: [''],
      bookName: [''],
      type: [''],
      status: ['open'],
      name: [''],
      dateOfBirth: [''],
      dateOfInjury: [''],
      providerOrLawFirm: [''],
    });

    // Subscribe to form changes for real-time filtering
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  loadData(): void {
    // Load sample data - replace with real service
    this.dataSource.data = this.sampleBooks;
  }

  loadUnderwriters(): void {
    // Load underwriters list - replace with real service
    this.underwriters = [
      { id: 1, name: 'Sarah Johnson', email: '<EMAIL>' },
      { id: 2, name: 'Mike Wilson', email: '<EMAIL>' },
      { id: 3, name: 'Lisa Chen', email: '<EMAIL>' },
    ];

    this.openBooks = [
      'Motor Vehicle Cases - Q1 2024',
      'Medical Malpractice - December',
      'Product Liability - January',
      'Workers Comp - Q4 2023',
      'Slip and Fall Cases - February',
    ];
  }

  applyFilters(): void {
    const filters = this.filterForm.value;
    let filteredData = [...this.sampleBooks];

    // Apply book-level filters
    if (filters.bookName) {
      filteredData = filteredData.filter((book) =>
        book.bookName.toLowerCase().includes(filters.bookName.toLowerCase()),
      );
    }

    if (filters.type) {
      filteredData = filteredData.filter(
        (book) => book.bookType === filters.type,
      );
    }

    // Apply plaintiff-level filters by filtering plaintiffs within books
    if (
      filters.name ||
      filters.dateOfBirth ||
      filters.dateOfInjury ||
      filters.providerOrLawFirm
    ) {
      filteredData = filteredData
        .map((book) => ({
          ...book,
          plaintiffs: book.plaintiffs.filter((plaintiff) => {
            let matches = true;

            if (filters.name) {
              const fullName =
                `${plaintiff.firstName} ${plaintiff.lastName}`.toLowerCase();
              matches =
                matches && fullName.includes(filters.name.toLowerCase());
            }

            if (filters.providerOrLawFirm) {
              matches =
                matches &&
                plaintiff.lawFirm
                  .toLowerCase()
                  .includes(filters.providerOrLawFirm.toLowerCase());
            }

            return matches;
          }),
        }))
        .filter((book) => book.plaintiffs.length > 0);
    }

    this.dataSource.data = filteredData;
  }

  toggleBookExpansion(book: UWBook): void {
    book.expanded = !book.expanded;
  }

  onBookTypeToggle(type: string): void {
    this.filterForm.patchValue({
      type: type === this.filterForm.value.type ? '' : type,
    });
  }

  onStatusToggle(status: string): void {
    this.filterForm.patchValue({
      status: status === this.filterForm.value.status ? '' : status,
    });
  }

  openDataGathering(bookId: string): void {
    // Navigate to data gathering for specific book
    console.log('Opening data gathering for book:', bookId);
  }

  openEvaluation(bookId: string): void {
    // Navigate to evaluation for specific book
    console.log('Opening evaluation for book:', bookId);
  }

  openPlaintiffDetail(plaintiffId: string): void {
    // Navigate to plaintiff detail
    console.log('Opening plaintiff detail for:', plaintiffId);
  }

  clearFilters(): void {
    this.filterForm.reset({
      uwName: '',
      bookName: '',
      type: '',
      status: 'open',
      name: '',
      dateOfBirth: '',
      dateOfInjury: '',
      providerOrLawFirm: '',
    });
  }
}

import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  DuplicateOpportunitiesMatches,
  OpportunityConfig,
  OpportunityWarning,
  ProviderAccount,
} from 'src/app/model/opportunity';
import config from '../../../app.config.json';
import { AuthenticationService } from '../authentication';
import { TailClaimStatus } from './opportunity.models';
import { FundingResponse } from 'src/app/model/fundings';
import { Case } from 'src/app/model';
import { PatientAttachment } from 'src/app/model/patients';
import { PatientSearchGridResponse } from 'src/app/model/patients/patient-search-grid-response';
import { Observable } from 'rxjs';

// UW Data Gathering Interfaces
export interface UWOpportunityDetails {
  id: string;
  name: string;
  stageName: string;
  amount: number;
  closeDate: Date;
  accountId: string;
  account: {
    name: string;
  };
  dateOfInjury: Date;
  accidentState: string;
  caseType: string;
  injurySeverity: number;
  liabilityPercentage: number;
  medicalLimit: string;
  claimNumber: string;
  accidentReportNumber: string;
  injuries: string;
  injuryDetails: string;
  incidentDescription: string;
}

export interface UWInsuranceInfo {
  insuranceCompany1: string;
  insuranceCompany2: string;
  insuranceLimits1: string;
  insuranceLimits2: string;
  policyType1: string;
  policyType2: string;
  decPage: boolean;
  decPageVerbal: boolean;
}

export interface UWPlaintiffDetails {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  address: string;
  city: string;
  state: string;
  zip: string;
  email: string;
  phone: string;
}

@Injectable({
  providedIn: 'root',
})
export class OpportunityService {
  private apiUrl = '';

  constructor(
    private http: HttpClient,
    private authService: AuthenticationService,
  ) {
    this.apiUrl = config.apiUrl;
  }

  getOpportunityCommentsByAccount(
    opportunityId: string,
    accountOwnerId: number,
  ) {
    const params = new HttpParams()
      .set('opportunityId', opportunityId)
      .set('accountId', accountOwnerId.toString());

    return this.http.get<any>(`${this.apiUrl}/api/opportunities/comments`, {
      params: params,
    });
  }

  getOpportunityWarnings(opportunityId: string) {
    const params = new HttpParams().set('opportunityId', opportunityId);

    return this.http.get<OpportunityWarning>(
      `${this.apiUrl}/api/opportunities/warning`,
      {
        params: params,
      },
    );
  }

  updateTreatmentComplete(config: OpportunityConfig) {
    const params = new HttpParams().set(
      'sharerID',
      this.authService.sharerUserIDValue.toString(),
    );

    return this.http.post<any>(
      `${this.apiUrl}/api/opportunities/treatment-complete`,
      {
        OpportunityID: config.opportunityID,
        TreatmentComplete: config.treatmentComplete,
      },
      {
        params: params,
      },
    );
  }

  updateAccidentState(opportunityID: string, state: string) {
    return this.http.post<any>(
      `${this.apiUrl}/api/opportunities/update-accident-state`,
      {
        OpportunityID: opportunityID,
        State: state,
      },
    );
  }

  getTreatmentComplete(opportunityId: string) {
    const params = new HttpParams().set('opportunityId', opportunityId);

    return this.http.get<any>(
      `${this.apiUrl}/api/opportunities/treatment-complete`,
      { params: params },
    );
  }

  getTailClaimStatus(opportunityId: string) {
    const params = new HttpParams().set('opportunityId', opportunityId);

    return this.http.get<TailClaimStatus>(
      `${this.apiUrl}/api/opportunities/tail-claim-status`,
      { params: params },
    );
  }

  addZeroFunding(opportunityId: string, medicalChart: string) {
    const params = new HttpParams().set('opportunityId', opportunityId);

    return this.http.post<any>(
      `${this.apiUrl}/api/opportunities/add-zero-funding`,
      { medicalChart },
      { params: params },
    );
  }

  getWatchers(opportunityID: string) {
    return this.http.get<any>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/watchers`,
    );
  }

  updateOpportunityWatching(
    opportunityID: string,
    value: boolean,
    optionID: number,
    overrideNotifications = false,
  ) {
    const params = new HttpParams()
      .set('value', JSON.stringify(value))
      .set('optionID', optionID.toString())
      .set('overrideNotifications', JSON.stringify(overrideNotifications));

    return this.http.put<any>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/watch`,
      null,
      { params: params, responseType: 'text' as 'json' },
    );
  }

  updateOpportunityWatchingAssigned(
    opportunityID: string,
    value: boolean,
    userID: number,
  ) {
    const params = new HttpParams()
      .set('value', JSON.stringify(value))
      .set('userID', userID.toString());

    return this.http.post<any>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/watch-assigned`,
      null,
      { params: params, responseType: 'text' as 'json' },
    );
  }

  getFundingsByOpportunity(opportunityID: string) {
    return this.http.get<FundingResponse[]>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/fundings`,
    );
  }

  getCasesByOpportunity(opportunityID: string) {
    return this.http.get<Case[]>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/cases`,
    );
  }

  getAttachmentsByOpportunity(opportunityID: string) {
    return this.http.get<PatientAttachment[]>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/attachments`,
    );
  }

  getProvidersByOpportunity(opportunityID: string) {
    return this.http.get<ProviderAccount[]>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/providers`,
    );
  }

  getPotentialDuplicates(opportunityID: string) {
    return this.http.get<PatientSearchGridResponse[]>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/potential-duplicates`,
    );
  }

  getPotentialDuplicatesSeparated(opportunityID: string) {
    return this.http.get<DuplicateOpportunitiesMatches>(
      `${this.apiUrl}/api/opportunities/${opportunityID}/potential-duplicates-separated`,
    );
  }

  // UW Data Gathering Methods
  getUWOpportunityDetails(
    opportunityId: string,
  ): Observable<UWOpportunityDetails> {
    return this.http.get<UWOpportunityDetails>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/uw-details`,
    );
  }

  getUWInsuranceInfo(opportunityId: string): Observable<UWInsuranceInfo> {
    return this.http.get<UWInsuranceInfo>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/uw-insurance`,
    );
  }

  getUWPlaintiffDetails(opportunityId: string): Observable<UWPlaintiffDetails> {
    return this.http.get<UWPlaintiffDetails>(
      `${this.apiUrl}/api/opportunities/${opportunityId}/uw-plaintiff`,
    );
  }
}

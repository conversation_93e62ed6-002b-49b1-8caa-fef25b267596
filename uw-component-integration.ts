// UW Data Gathering Component Integration Plan

export class UWDataGatheringComponent implements OnInit {
  // Add new properties for Salesforce data
  opportunityDetails: OpportunityDetails | null = null;
  plaintiffDetails: PlaintiffDetails | null = null;
  insuranceInfo: InsuranceInfo | null = null;
  caseInfo: CaseInfo | null = null;
  fundingRecords: FundingRecord[] = [];
  underwritingMetrics: UnderwritingMetrics | null = null;
  lawFirmInfo: LawFirmInfo | null = null;
  attorneyInfo: AttorneyInfo | null = null;
  medicalProviders: ProviderAccount[] = [];
  attachments: PatientAttachment[] = [];

  constructor(
    private opportunityService: OpportunityService,
    private attorneyService: AttorneyService,
    // ... existing services
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.initializeDocumentChecklist();
    this.setupAutoSave();
    this.setupFormValueChanges();
    
    // Replace mock data loading with real Salesforce data
    this.loadAllSalesforceData();
  }

  loadAllSalesforceData(): void {
    if (!this.currentOpportunityId) {
      console.warn('No opportunity ID available for Salesforce data loading');
      return;
    }

    // Load all data in parallel
    this.loadOpportunityDetails();
    this.loadPlaintiffDetails();
    this.loadInsuranceInfo();
    this.loadCaseInformation();
    this.loadLawFirmAndAttorney();
    this.loadFundingRecords();
    this.loadMedicalProviders();
    this.loadAttachments();
    this.loadCaseNotes(); // Already implemented
    this.loadUnderwritingMetrics();
  }

  loadOpportunityDetails(): void {
    this.opportunityService.getOpportunityDetails(this.currentOpportunityId)
      .subscribe({
        next: (details) => {
          this.opportunityDetails = details;
          this.populateOpportunityFields(details);
          console.log('Loaded opportunity details:', details);
        },
        error: (error) => {
          console.error('Error loading opportunity details:', error);
        }
      });
  }

  loadPlaintiffDetails(): void {
    this.opportunityService.getPlaintiffDetails(this.currentOpportunityId)
      .subscribe({
        next: (plaintiff) => {
          this.plaintiffDetails = plaintiff;
          this.populatePlaintiffFields(plaintiff);
          console.log('Loaded plaintiff details:', plaintiff);
        },
        error: (error) => {
          console.error('Error loading plaintiff details:', error);
        }
      });
  }

  loadInsuranceInfo(): void {
    this.opportunityService.getInsuranceInfo(this.currentOpportunityId)
      .subscribe({
        next: (insurance) => {
          this.insuranceInfo = insurance;
          this.populateInsuranceFields(insurance);
          console.log('Loaded insurance info:', insurance);
        },
        error: (error) => {
          console.error('Error loading insurance info:', error);
        }
      });
  }

  loadCaseInformation(): void {
    this.opportunityService.getCaseInformation(this.currentOpportunityId)
      .subscribe({
        next: (caseInfo) => {
          this.caseInfo = caseInfo;
          this.populateCaseFields(caseInfo);
          console.log('Loaded case information:', caseInfo);
        },
        error: (error) => {
          console.error('Error loading case information:', error);
        }
      });
  }

  loadLawFirmAndAttorney(): void {
    // Load law firm info
    this.attorneyService.getLawFirmByOpportunity(this.currentOpportunityId)
      .subscribe({
        next: (lawFirm) => {
          this.lawFirmInfo = lawFirm;
          this.populateLawFirmFields(lawFirm);
          console.log('Loaded law firm info:', lawFirm);
        },
        error: (error) => {
          console.error('Error loading law firm info:', error);
        }
      });

    // Load attorney info
    this.attorneyService.getAttorneyByOpportunity(this.currentOpportunityId)
      .subscribe({
        next: (attorney) => {
          this.attorneyInfo = attorney;
          this.populateAttorneyFields(attorney);
          console.log('Loaded attorney info:', attorney);
        },
        error: (error) => {
          console.error('Error loading attorney info:', error);
        }
      });
  }

  loadFundingRecords(): void {
    this.opportunityService.getFundingRecords(this.currentOpportunityId)
      .subscribe({
        next: (records) => {
          this.fundingRecords = records;
          this.populateFundingFields(records);
          console.log('Loaded funding records:', records);
        },
        error: (error) => {
          console.error('Error loading funding records:', error);
        }
      });
  }

  loadMedicalProviders(): void {
    this.opportunityService.getProvidersByOpportunity(this.currentOpportunityId)
      .subscribe({
        next: (providers) => {
          this.medicalProviders = providers;
          this.populateProviderFields(providers);
          console.log('Loaded medical providers:', providers);
        },
        error: (error) => {
          console.error('Error loading medical providers:', error);
        }
      });
  }

  loadAttachments(): void {
    this.opportunityService.getAttachmentsByOpportunity(this.currentOpportunityId)
      .subscribe({
        next: (attachments) => {
          this.attachments = attachments;
          this.categorizeAttachments(attachments);
          console.log('Loaded attachments:', attachments);
        },
        error: (error) => {
          console.error('Error loading attachments:', error);
        }
      });
  }

  loadUnderwritingMetrics(): void {
    this.opportunityService.getUnderwritingMetrics(this.currentOpportunityId)
      .subscribe({
        next: (metrics) => {
          this.underwritingMetrics = metrics;
          this.displayUnderwritingMetrics(metrics);
          console.log('Loaded underwriting metrics:', metrics);
        },
        error: (error) => {
          console.error('Error loading underwriting metrics:', error);
        }
      });
  }

  // Field population methods
  populateOpportunityFields(details: OpportunityDetails): void {
    this.dataGatheringForm.patchValue({
      injurySeverity: details.injurySeverity,
      liabilityPercentage: details.liabilityPercentage,
    });
  }

  populateInsuranceFields(insurance: InsuranceInfo): void {
    this.dataGatheringForm.patchValue({
      insuranceCarrier1: insurance.insuranceCompany1,
      insuranceCarrier2: insurance.insuranceCompany2,
      decPage: insurance.decPage,
      decPageVerbal: insurance.decPageVerbal,
    });
  }

  populateCaseFields(caseInfo: CaseInfo): void {
    this.dataGatheringForm.patchValue({
      incidentDescription: caseInfo.incidentDescription,
      injuryDetail: caseInfo.injuryDetails,
      policeReportDetails: caseInfo.policeReportDetails,
    });
  }

  populateLawFirmFields(lawFirm: LawFirmInfo): void {
    this.dataGatheringForm.patchValue({
      lawFirm: lawFirm.id,
    });
    
    // Mark law firm as completed
    const lawFirmDoc = this.legalDocuments.find(doc => doc.id === 'law_firm');
    if (lawFirmDoc) {
      lawFirmDoc.completed = true;
      lawFirmDoc.dateCompleted = new Date();
    }
  }

  populateAttorneyFields(attorney: AttorneyInfo): void {
    this.dataGatheringForm.patchValue({
      attorney: attorney.id,
    });
    
    // Mark attorney as completed
    const attorneyDoc = this.legalDocuments.find(doc => doc.id === 'attorney');
    if (attorneyDoc) {
      attorneyDoc.completed = true;
      attorneyDoc.dateCompleted = new Date();
    }
  }

  categorizeAttachments(attachments: PatientAttachment[]): void {
    // Categorize attachments by document type
    const medicalDocs = attachments.filter(att => 
      ['Bills/ Claims', 'Patient Ledger', 'Other'].includes(att.documentType)
    );
    
    const legalDocs = attachments.filter(att => 
      ['Contracts', 'Settlement Agreement', 'Accident Report'].includes(att.documentType)
    );

    // Update document counts
    this.dataGatheringForm.patchValue({
      medicalDocumentsCount: medicalDocs.length,
      medicalDocumentsAvailable: medicalDocs.length > 0,
    });

    // Mark document categories as completed based on attachments
    this.updateDocumentCompletionStatus(attachments);
  }

  updateDocumentCompletionStatus(attachments: PatientAttachment[]): void {
    // Update medical documents status
    const medicalDoc = this.medicalDocuments.find(doc => doc.id === 'medical_documents');
    if (medicalDoc && attachments.some(att => att.documentType === 'Bills/ Claims')) {
      medicalDoc.completed = true;
      medicalDoc.dateCompleted = new Date();
    }

    // Update other document statuses based on attachment types
    // Add more logic as needed for specific document types
  }
}

import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from 'src/app/services/user';
import { SidebarItem } from 'src/app/shared/collapsible-sidebar/collapsible-sidebar.component';

@Component({
  selector: 'app-admin-todo-page',
  templateUrl: './admin-todo-page.component.html',
  styleUrls: ['./admin-todo-page.component.scss'],
})
export class AdminTodoPageComponent implements OnInit {
  categories: SidebarItem[] = [];
  isCollapsed = false;

  enableAdminFeature = window.enableAdminFeature;

  constructor(
    private userService: UserService,
    public router: Router,
  ) {}

  ngOnInit(): void {
    this.userService.getActiveUserPermissions();
    this.initializeCategories();
  }

  initializeCategories(): void {
    if (this.enableAdminFeature)
      this.categories.push({
        value: 'Search Plaintiffs',
        icon: 'search',
        link: '/admin/todo/search-plaintiff',
      });

    this.categories.push({
      value: 'Reduction Request',
      icon: 'request_quote',
      link: '/admin/todo/reduction-request',
    });
    this.categories.push({
      value: 'Payoff Approval Request',
      icon: 'approval',
      link: '/admin/todo/payoff-approval',
    });
    this.categories.push({
      value: 'Underwriting',
      icon: 'assessment',
      link: '/admin/todo/underwriting',
    });
    /*this.categories.push({
      value: 'Data Manual Review',
      selected: false,
      icon: 'data_object',
      link: '/admin/todo/data-manual-review',
    });*/
  }

  onToggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
  }
}
